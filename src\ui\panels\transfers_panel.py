"""
Pannello gestione trasferimenti e mercato
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QGroupBox, QTabWidget, QComboBox, QPushButton,
                            QSpinBox, QLineEdit, QTextEdit, QSlider)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..components.data_table import DataTable
from ..components.info_display import InfoDisplay
from ..components.action_buttons import ActionButtons
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR,
                           MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class TransfersPanel(QWidget):
    """Pannello per gestione trasferimenti"""

    transfer_requested = pyqtSignal(dict)
    offer_made = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.available_players = []
        self.transfer_targets = []
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        self.create_panel_header(main_layout)

        # Tab widget principale
        self.tab_widget = QTabWidget()

        # Tab Mercato
        self.create_market_tab()

        # Tab Obiettivi
        self.create_targets_tab()

        # Tab Trattative
        self.create_negotiations_tab()

        main_layout.addWidget(self.tab_widget)

    def create_panel_header(self, parent_layout):
        """Crea l'header del pannello"""
        header_layout = QHBoxLayout()

        # Titolo
        title_label = QLabel("Mercato Trasferimenti")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Budget disponibile
        self.budget_label = QLabel("Budget: €0")
        self.budget_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                color: {COLOR_SUCCESS};
                padding: 5px 10px;
                border: 1px solid {COLOR_SUCCESS};
                border-radius: 4px;
            }}
        """)
        header_layout.addWidget(self.budget_label)

        # Stato mercato
        self.market_status_label = QLabel("Mercato: Aperto")
        self.market_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                color: {COLOR_WARNING};
                padding: 5px 10px;
                border: 1px solid {COLOR_WARNING};
                border-radius: 4px;
            }}
        """)
        header_layout.addWidget(self.market_status_label)

        parent_layout.addLayout(header_layout)

    def create_market_tab(self):
        """Crea la tab mercato"""
        market_widget = QWidget()
        layout = QVBoxLayout(market_widget)

        # Filtri ricerca
        filters_layout = QHBoxLayout()
        
        filters_layout.addWidget(QLabel("Ruolo:"))
        self.position_filter = QComboBox()
        self.position_filter.addItems([
            "Tutti", "Portieri", "Difensori", "Centrocampisti", "Attaccanti"
        ])
        self.position_filter.currentTextChanged.connect(self.filter_players)
        filters_layout.addWidget(self.position_filter)

        filters_layout.addWidget(QLabel("Età max:"))
        self.age_filter = QSpinBox()
        self.age_filter.setMinimum(16)
        self.age_filter.setMaximum(40)
        self.age_filter.setValue(35)
        self.age_filter.valueChanged.connect(self.filter_players)
        filters_layout.addWidget(self.age_filter)

        filters_layout.addWidget(QLabel("Budget max:"))
        self.budget_filter = QSlider(Qt.Orientation.Horizontal)
        self.budget_filter.setMinimum(0)
        self.budget_filter.setMaximum(100000000)  # 100M
        self.budget_filter.setValue(50000000)  # 50M default
        self.budget_filter.valueChanged.connect(self.update_budget_filter_label)
        filters_layout.addWidget(self.budget_filter)

        self.budget_filter_label = QLabel("€50M")
        filters_layout.addWidget(self.budget_filter_label)

        filters_layout.addStretch()
        layout.addLayout(filters_layout)

        # Tabella giocatori disponibili
        columns = [
            {"key": "nome", "title": "Nome", "width": 120, "stretch": True},
            {"key": "cognome", "title": "Cognome", "width": 120},
            {"key": "eta", "title": "Età", "width": 50, "align": "center"},
            {"key": "posizione", "title": "Ruolo", "width": 60, "align": "center"},
            {"key": "squadra", "title": "Squadra", "width": 120},
            {"key": "valore", "title": "Valore", "width": 100, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}" if isinstance(x, (int, float)) else "N/A"},
            {"key": "contratto", "title": "Contratto", "width": 80, "align": "center"}
        ]

        self.market_table = DataTable(columns)
        self.market_table.row_selected.connect(self.on_player_selected)
        layout.addWidget(self.market_table)

        # Azioni giocatore
        actions_layout = QHBoxLayout()
        
        self.add_target_btn = QPushButton("Aggiungi agli Obiettivi")
        self.add_target_btn.setEnabled(False)
        self.add_target_btn.clicked.connect(self.add_to_targets)
        actions_layout.addWidget(self.add_target_btn)

        self.make_offer_btn = QPushButton("Fai Offerta")
        self.make_offer_btn.setEnabled(False)
        self.make_offer_btn.clicked.connect(self.make_offer)
        actions_layout.addWidget(self.make_offer_btn)

        actions_layout.addStretch()
        layout.addLayout(actions_layout)

        # Carica dati di esempio
        self.load_sample_players()

        self.tab_widget.addTab(market_widget, "🛒 Mercato")

    def create_targets_tab(self):
        """Crea la tab obiettivi"""
        targets_widget = QWidget()
        layout = QVBoxLayout(targets_widget)

        # Lista obiettivi
        targets_group = QGroupBox("Lista Obiettivi")
        targets_layout = QVBoxLayout(targets_group)

        self.targets_table = DataTable([
            {"key": "nome", "title": "Nome", "width": 120, "stretch": True},
            {"key": "cognome", "title": "Cognome", "width": 120},
            {"key": "posizione", "title": "Ruolo", "width": 60, "align": "center"},
            {"key": "squadra", "title": "Squadra", "width": 120},
            {"key": "valore", "title": "Valore", "width": 100, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}" if isinstance(x, (int, float)) else "N/A"},
            {"key": "priorita", "title": "Priorità", "width": 80, "align": "center"}
        ])
        targets_layout.addWidget(self.targets_table)

        layout.addWidget(targets_group)

        # Azioni obiettivi
        targets_actions = ActionButtons('horizontal')
        targets_actions.add_button("Rimuovi", "remove_target", "danger")
        targets_actions.add_button("Cambia Priorità", "change_priority", "secondary")
        targets_actions.add_button("Avvia Trattativa", "start_negotiation", "primary")
        layout.addWidget(targets_actions)

        layout.addStretch()

        self.tab_widget.addTab(targets_widget, "🎯 Obiettivi")

    def create_negotiations_tab(self):
        """Crea la tab trattative"""
        negotiations_widget = QWidget()
        layout = QVBoxLayout(negotiations_widget)

        # Trattative attive
        active_group = QGroupBox("Trattative Attive")
        active_layout = QVBoxLayout(active_group)

        self.negotiations_table = DataTable([
            {"key": "giocatore", "title": "Giocatore", "width": 150, "stretch": True},
            {"key": "squadra", "title": "Squadra", "width": 120},
            {"key": "offerta", "title": "Nostra Offerta", "width": 100, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}" if isinstance(x, (int, float)) else "N/A"},
            {"key": "richiesta", "title": "Richiesta", "width": 100, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}" if isinstance(x, (int, float)) else "N/A"},
            {"key": "stato", "title": "Stato", "width": 100, "align": "center"},
            {"key": "scadenza", "title": "Scadenza", "width": 100, "align": "center"}
        ])
        active_layout.addWidget(self.negotiations_table)

        layout.addWidget(active_group)

        # Dettagli trattativa
        details_group = QGroupBox("Dettagli Trattativa")
        details_layout = QVBoxLayout(details_group)

        self.negotiation_details = InfoDisplay()
        details_layout.addWidget(self.negotiation_details)

        # Azioni trattativa
        negotiation_actions = QHBoxLayout()
        
        self.improve_offer_btn = QPushButton("Migliora Offerta")
        self.improve_offer_btn.setEnabled(False)
        negotiation_actions.addWidget(self.improve_offer_btn)

        self.withdraw_offer_btn = QPushButton("Ritira Offerta")
        self.withdraw_offer_btn.setEnabled(False)
        negotiation_actions.addWidget(self.withdraw_offer_btn)

        negotiation_actions.addStretch()
        details_layout.addLayout(negotiation_actions)

        layout.addWidget(details_group)

        self.tab_widget.addTab(negotiations_widget, "💼 Trattative")

    def load_sample_players(self):
        """Carica giocatori di esempio"""
        sample_players = [
            {
                "nome": "Luka", "cognome": "Modric", "eta": 38,
                "posizione": "CC", "squadra": "Real Madrid", 
                "valore": 15000000, "contratto": "2025"
            },
            {
                "nome": "Kylian", "cognome": "Mbappe", "eta": 25,
                "posizione": "C", "squadra": "PSG", 
                "valore": 180000000, "contratto": "2025"
            },
            {
                "nome": "Erling", "cognome": "Haaland", "eta": 24,
                "posizione": "C", "squadra": "Manchester City", 
                "valore": 170000000, "contratto": "2027"
            },
            {
                "nome": "Pedri", "cognome": "González", "eta": 21,
                "posizione": "CC", "squadra": "Barcelona", 
                "valore": 80000000, "contratto": "2026"
            }
        ]
        
        self.available_players = sample_players
        self.market_table.set_data(sample_players)

    def filter_players(self):
        """Filtra i giocatori disponibili"""
        position = self.position_filter.currentText()
        max_age = self.age_filter.value()
        max_budget = self.budget_filter.value()

        filtered_players = []
        for player in self.available_players:
            # Filtro età
            if player['eta'] > max_age:
                continue
            
            # Filtro budget
            if player['valore'] > max_budget:
                continue
            
            # Filtro posizione
            if position != "Tutti":
                position_map = {
                    "Portieri": ["P"],
                    "Difensori": ["DC", "DS", "DD", "T"],
                    "Centrocampisti": ["CC", "CD", "CO", "E"],
                    "Attaccanti": ["C", "A", "PC"]
                }
                if player['posizione'] not in position_map.get(position, []):
                    continue
            
            filtered_players.append(player)

        self.market_table.set_data(filtered_players)

    def update_budget_filter_label(self, value):
        """Aggiorna l'etichetta del filtro budget"""
        if value >= 1000000:
            self.budget_filter_label.setText(f"€{value/1000000:.0f}M")
        else:
            self.budget_filter_label.setText(f"€{value:,.0f}")
        self.filter_players()

    def on_player_selected(self, row_index, player_data):
        """Gestisce la selezione di un giocatore"""
        self.selected_player = player_data
        self.add_target_btn.setEnabled(True)
        self.make_offer_btn.setEnabled(True)

    def add_to_targets(self):
        """Aggiunge un giocatore agli obiettivi"""
        if hasattr(self, 'selected_player'):
            target_data = self.selected_player.copy()
            target_data['priorita'] = 'Media'
            self.transfer_targets.append(target_data)
            self.targets_table.set_data(self.transfer_targets)

    def make_offer(self):
        """Fa un'offerta per un giocatore"""
        if hasattr(self, 'selected_player'):
            offer_data = {
                'player': self.selected_player,
                'offer_amount': self.selected_player['valore'] * 0.8,  # 80% del valore
                'type': 'transfer'
            }
            self.offer_made.emit(offer_data)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data
        club_data = game_data.get('selected_club', {})
        
        # Aggiorna budget
        budget = club_data.get('budget_trasferimenti_eur', 0)
        self.budget_label.setText(f"Budget: €{budget:,.0f}")

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'transfers',
            'available_players_count': len(self.available_players),
            'targets_count': len(self.transfer_targets)
        }

"""
Funzioni utility comuni per Football Manager Italiano
"""
import random
import datetime
from typing import List, Dict, Any, Optional, Union
import json
import logging
from pathlib import Path

from .config import *

def setup_logging():
    """Configura il sistema di logging"""
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format=LOG_FORMAT,
        handlers=[
            logging.FileHandler(LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

def generate_random_name() -> tuple:
    """Genera un nome e cognome casuale italiano"""
    first_names = [
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>",
        "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
    ]

    last_names = [
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> <PERSON>", "<PERSON><PERSON>",
        "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ra", "<PERSON>alli", "<PERSON>i"
    ]

    return random.choice(first_names), random.choice(last_names)

def calculate_age_from_birth_year(birth_year: int) -> int:
    """Calcola l'età da anno di nascita"""
    current_year = datetime.datetime.now().year
    return current_year - birth_year

def generate_birth_year_for_age_range(min_age: int, max_age: int) -> int:
    """Genera anno di nascita per fascia età"""
    current_year = datetime.datetime.now().year
    return current_year - random.randint(min_age, max_age)

def format_number_italian(number: Union[int, float]) -> str:
    """Formatta un numero in stile italiano con separatori"""
    if isinstance(number, float):
        return f"{number:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
    else:
        return f"{number:,}".replace(",", ".")

def format_percentage(value: float) -> str:
    """Formatta una percentuale"""
    return f"{value:.1f}%"

def format_currency(amount: Union[int, float], currency: str = "€") -> str:
    """Formatta un importo in valuta europea"""
    if amount >= 1_000_000:
        return f"{currency}{amount/1_000_000:.1f}M"
    elif amount >= 1_000:
        return f"{currency}{amount/1_000:.0f}K"
    else:
        return f"{currency}{amount:,.0f}".replace(",", ".")

def clamp(value: Union[int, float], min_val: Union[int, float], max_val: Union[int, float]) -> Union[int, float]:
    """Limita un valore tra min e max"""
    return max(min_val, min(value, max_val))

def weighted_random_choice(items: List, weights: List[float]) -> Any:
    """Scelta casuale pesata"""
    if len(items) != len(weights):
        raise ValueError("Items e weights devono avere la stessa lunghezza")

    total_weight = sum(weights)
    if total_weight == 0:
        return random.choice(items)

    weights = [w / total_weight for w in weights]

    r = random.random()
    cumulative = 0
    for item, weight in zip(items, weights):
        cumulative += weight
        if r <= cumulative:
            return item

    return items[-1]

def normalize_name(name: str) -> str:
    """Normalizza un nome rimuovendo spazi e caratteri speciali"""
    return name.strip().title()

def get_season_string(start_year: int) -> str:
    """Restituisce stringa stagione nel formato 2024/2025"""
    return f"{start_year}/{start_year + 1}"

def get_match_day_name(day_number: int) -> str:
    """Restituisce il nome della giornata"""
    return f"Giornata {day_number}"

def calculate_contract_value(base_salary: int, years: int, bonuses: int = 0) -> int:
    """Calcola valore totale contratto"""
    return (base_salary * years) + bonuses

def generate_unique_id() -> str:
    """Genera ID univoco basato su timestamp"""
    import time
    return str(int(time.time() * 1000000))

def load_json_file(file_path: Path) -> Optional[Dict]:
    """Carica un file JSON in modo sicuro"""
    try:
        if not file_path.exists():
            logger.warning(f"File JSON non trovato: {file_path}")
            return None

        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"File JSON caricato: {file_path}")
        return data
    except json.JSONDecodeError as e:
        logger.error(f"Errore parsing JSON {file_path}: {e}")
        return None
    except Exception as e:
        logger.error(f"Errore caricamento file {file_path}: {e}")
        return None

def save_json_file(data: Dict, file_path: Path) -> bool:
    """Salva dati in file JSON"""
    try:
        # Crea directory parent se non esistono
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"File JSON salvato: {file_path}")
        return True
    except Exception as e:
        logger.error(f"Errore salvataggio file {file_path}: {e}")
        return False

def calculate_reputation_change(current_rep: int, performance_factor: float) -> int:
    """Calcola cambio reputazione basato su performance"""
    change = int(performance_factor * 2)
    new_rep = clamp(current_rep + change, 1, 20)
    return new_rep - current_rep

def get_age_category(age: int) -> str:
    """Restituisce categoria d'età"""
    if age <= 21:
        return "giovane"
    elif age <= 29:
        return "adulto"
    else:
        return "veterano"

def calculate_player_potential(current_rating: int, age: int) -> int:
    """Calcola potenziale giocatore basato su età"""
    if age <= 18:
        return clamp(current_rating + random.randint(3, 8), current_rating, 20)
    elif age <= 21:
        return clamp(current_rating + random.randint(1, 5), current_rating, 20)
    elif age <= 25:
        return clamp(current_rating + random.randint(0, 3), current_rating, 20)
    else:
        return current_rating

def format_duration_minutes(minutes: int) -> str:
    """Formatta durata in minuti in formato leggibile"""
    hours = minutes // 60
    mins = minutes % 60
    if hours > 0:
        return f"{hours}h {mins}m"
    else:
        return f"{mins}m"

def get_injury_duration() -> int:
    """Restituisce durata infortunio casuale in giorni"""
    injury_types = {
        "lieve": (3, 10),
        "media": (10, 30),
        "grave": (30, 90),
        "molto_grave": (90, 180)
    }

    weights = [0.5, 0.3, 0.15, 0.05]  # Probabilità per tipo infortunio
    injury_type = weighted_random_choice(list(injury_types.keys()), weights)
    min_days, max_days = injury_types[injury_type]

    return random.randint(min_days, max_days)

def validate_email(email: str) -> bool:
    """Validazione semplice email"""
    return "@" in email and "." in email.split("@")[-1]

def truncate_string(text: str, max_length: int) -> str:
    """Tronca stringa se troppo lunga"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def get_ordinal_number(number: int) -> str:
    """Restituisce numero ordinale in italiano"""
    return f"{number}°"

def calculate_distance_between_cities(city1: str, city2: str) -> int:
    """Calcola distanza approssimativa tra città (simulata)"""
    # Simulazione semplice - in un gioco reale useresti coordinate GPS
    return random.randint(50, 800)

def generate_match_attendance(capacity: int, team_reputation: int, weather: str = "buono") -> int:
    """Genera affluenza partita basata su vari fattori"""
    base_attendance = capacity * 0.6  # 60% base

    # Fattore reputazione
    reputation_factor = team_reputation / 20

    # Fattore meteo
    weather_factor = {"buono": 1.0, "pioggia": 0.9, "neve": 0.8}.get(weather, 1.0)

    # Calcolo finale con variazione casuale
    final_attendance = int(base_attendance * reputation_factor * weather_factor * random.uniform(0.8, 1.2))

    return clamp(final_attendance, capacity * 0.3, capacity)

class Timer:
    """Classe utility per misurare tempi di esecuzione"""

    def __init__(self):
        self.start_time = None

    def start(self):
        """Avvia il timer"""
        self.start_time = datetime.datetime.now()

    def stop(self) -> float:
        """Ferma il timer e restituisce secondi trascorsi"""
        if self.start_time is None:
            return 0.0

        elapsed = datetime.datetime.now() - self.start_time
        return elapsed.total_seconds()

    def stop_and_log(self, operation_name: str):
        """Ferma il timer e logga il tempo"""
        seconds = self.stop()
        logger.info(f"{operation_name} completato in {seconds:.2f} secondi")

def format_date_italian(date_obj: datetime.date) -> str:
    """Formatta una data in stile italiano (gg/mm/aaaa)"""
    if not date_obj:
        return ""
    return date_obj.strftime("%d/%m/%Y")

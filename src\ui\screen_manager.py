"""
Gestore delle schermate per il frame centrale
Gestisce il cambio dinamico di contenuto nel frame principale
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QStackedWidget
from PyQt6.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont

from ui.base_screen import BaseScreen
from core.config import COLOR_BACKGROUND


class ScreenManager(QWidget):
    """Gestisce le schermate nel frame centrale con transizioni"""

    screen_changed = pyqtSignal(str)  # Nome della schermata corrente

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_screen = None
        self.screen_history = []
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del gestore schermate"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Stack widget per contenere le schermate
        self.stacked_widget = QStackedWidget()
        layout.addWidget(self.stacked_widget)

        # Stile del container
        self.setStyleSheet(f"""
            ScreenManager {{
                background-color: {COLOR_BACKGROUND};
                border: none;
            }}
            QStackedWidget {{
                background-color: {COLOR_BACKGROUND};
                border: none;
            }}
        """)

    def show_screen(self, screen):
        """Mostra una nuova schermata nel frame centrale"""
        if not isinstance(screen, BaseScreen):
            raise ValueError("La schermata deve ereditare da BaseScreen")

        # Rimuovi schermata precedente se presente
        if self.current_screen:
            self.stacked_widget.removeWidget(self.current_screen)
            self.current_screen.setParent(None)

        # Aggiungi e mostra nuova schermata
        self.stacked_widget.addWidget(screen)
        self.stacked_widget.setCurrentWidget(screen)

        # Aggiorna riferimenti
        previous_screen = self.current_screen
        self.current_screen = screen

        # Aggiungi alla cronologia
        if previous_screen:
            screen_name = previous_screen.__class__.__name__
            if not self.screen_history or self.screen_history[-1] != screen_name:
                self.screen_history.append(screen_name)

        # Inizializza la nuova schermata
        screen.initialize()

        # Emetti segnale di cambio schermata
        self.screen_changed.emit(screen.__class__.__name__)

    def can_go_back(self):
        """Verifica se è possibile tornare alla schermata precedente"""
        return len(self.screen_history) > 0

    def go_back(self):
        """Torna alla schermata precedente (se implementato dalla schermata)"""
        if self.current_screen and hasattr(self.current_screen, 'go_back'):
            self.current_screen.go_back()

    def get_current_screen_name(self):
        """Restituisce il nome della schermata corrente"""
        if self.current_screen:
            return self.current_screen.__class__.__name__
        return None

    def clear_history(self):
        """Pulisce la cronologia delle schermate"""
        self.screen_history.clear()

    def refresh_current_screen(self):
        """Aggiorna la schermata corrente"""
        if self.current_screen and hasattr(self.current_screen, 'refresh'):
            self.current_screen.refresh()

    def set_screen_data(self, data):
        """Passa dati alla schermata corrente"""
        if self.current_screen and hasattr(self.current_screen, 'set_data'):
            self.current_screen.set_data(data)

    def get_screen_data(self):
        """Ottiene dati dalla schermata corrente"""
        if self.current_screen and hasattr(self.current_screen, 'get_data'):
            return self.current_screen.get_data()
        return {}

    def cleanup(self):
        """Pulisce le risorse del gestore schermate"""
        if self.current_screen:
            if hasattr(self.current_screen, 'cleanup'):
                self.current_screen.cleanup()
            self.current_screen.setParent(None)

        self.stacked_widget.clear()
        self.current_screen = None
        self.screen_history.clear()
"""
Schermata di creazione nuova partita
Configurazione iniziale del gioco
"""

import datetime
from PyQt6.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
                            QPushButton, QGroupBox, QSpinBox, QCheckBox,
                            QGridLayout, QSizePolicy, QSpacerItem)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..base_screen import BaseScreen
from ...core.config import (COLOR_PRIMARY, COLOR_SURFACE, COLOR_BORDER,
                           MAIN_FONT_FAMILY, MAIN_FONT_SIZE)


class NewGameScreen(BaseScreen):
    """Schermata per configurare una nuova partita"""

    game_created = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__("Nuova Partita", parent)

    def setup_content(self):
        """Configura il contenuto della schermata"""
        # Layout principale centrato
        main_container = QVBoxLayout()
        main_container.addStretch()

        # Container centrale per i controlli
        center_widget = QGroupBox()
        center_widget.setFixedWidth(600)
        center_widget.setMaximumHeight(500)
        center_widget.setStyleSheet(f"""
            QGroupBox {{
                background-color: {COLOR_SURFACE};
                border: 1px solid {COLOR_BORDER};
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }}
        """)

        center_layout = QVBoxLayout(center_widget)
        center_layout.setSpacing(25)

        # Titolo principale
        self.create_title(center_layout)

        # Configurazioni di gioco
        self.create_game_settings(center_layout)

        # Configurazioni difficoltà
        self.create_difficulty_settings(center_layout)

        # Pulsanti azioni
        self.create_action_buttons(center_layout)

        # Centra il widget principale
        h_layout = QHBoxLayout()
        h_layout.addStretch()
        h_layout.addWidget(center_widget)
        h_layout.addStretch()

        main_container.addLayout(h_layout)
        main_container.addStretch()

        self.add_content_layout(main_container)

    def create_title(self, layout):
        """Crea il titolo principale"""
        title = QLabel("Inizia la tua carriera manageriale!")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont(MAIN_FONT_FAMILY, 16, QFont.Weight.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title)

        subtitle = QLabel("Configura le impostazioni per la tua nuova partita")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        layout.addWidget(subtitle)

    def create_game_settings(self, layout):
        """Crea le configurazioni di base del gioco"""
        settings_group = QGroupBox("Impostazioni Partita")
        settings_layout = QGridLayout(settings_group)
        settings_layout.setSpacing(15)

        # Stagione di inizio
        settings_layout.addWidget(QLabel("Stagione di inizio:"), 0, 0)
        self.season_combo = QComboBox()
        current_year = datetime.datetime.now().year
        seasons = [f"{year}/{str(year+1)[2:]}" for year in range(current_year, current_year + 5)]
        self.season_combo.addItems(seasons)
        self.season_combo.setCurrentText("2025/26")
        settings_layout.addWidget(self.season_combo, 0, 1)

        # Campionato principale
        settings_layout.addWidget(QLabel("Campionato principale:"), 1, 0)
        self.league_combo = QComboBox()
        self.league_combo.addItems(["Serie A", "Serie B", "Serie C"])
        self.league_combo.setCurrentText("Serie A")
        settings_layout.addWidget(self.league_combo, 1, 1)

        # Competizioni internazionali
        self.international_check = QCheckBox("Abilita competizioni internazionali")
        self.international_check.setChecked(True)
        settings_layout.addWidget(self.international_check, 2, 0, 1, 2)

        layout.addWidget(settings_group)

    def create_difficulty_settings(self, layout):
        """Crea le impostazioni di difficoltà"""
        difficulty_group = QGroupBox("Difficoltà e Realismo")
        difficulty_layout = QGridLayout(difficulty_group)
        difficulty_layout.setSpacing(15)

        # Livello difficoltà
        difficulty_layout.addWidget(QLabel("Livello difficoltà:"), 0, 0)
        self.difficulty_combo = QComboBox()
        self.difficulty_combo.addItems([
            "Principiante", "Normale", "Difficile", "Leggendario"
        ])
        self.difficulty_combo.setCurrentText("Normale")
        difficulty_layout.addWidget(self.difficulty_combo, 0, 1)

        # Realismo finanziario
        self.financial_realism = QCheckBox("Realismo finanziario")
        self.financial_realism.setChecked(True)
        self.financial_realism.setToolTip("Budget realistici e Fair Play Finanziario")
        difficulty_layout.addWidget(self.financial_realism, 1, 0, 1, 2)

        # Infortuni realistici
        self.injury_realism = QCheckBox("Infortuni realistici")
        self.injury_realism.setChecked(True)
        difficulty_layout.addWidget(self.injury_realism, 2, 0, 1, 2)

        layout.addWidget(difficulty_group)

    def create_action_buttons(self, layout):
        """Crea i pulsanti di azione"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # Pulsante Inizia
        start_button = QPushButton("Inizia Partita")
        start_button.setFixedSize(150, 40)
        start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #0056b3;
            }}
            QPushButton:pressed {{
                background-color: #004494;
            }}
        """)
        start_button.clicked.connect(self.start_game)
        button_layout.addWidget(start_button)

        button_layout.addStretch()
        layout.addLayout(button_layout)

    def start_game(self):
        """Avvia una nuova partita con le configurazioni selezionate"""
        game_data = {
            'season': self.season_combo.currentText(),
            'main_league': self.league_combo.currentText(),
            'international_competitions': self.international_check.isChecked(),
            'difficulty': self.difficulty_combo.currentText(),
            'financial_realism': self.financial_realism.isChecked(),
            'injury_realism': self.injury_realism.isChecked(),
            'creation_date': datetime.datetime.now().isoformat()
        }

        self.game_created.emit(game_data)
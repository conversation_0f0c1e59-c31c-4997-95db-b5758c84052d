"""
Dashboard principale del gioco con menu a tendina e pannelli riutilizzabili
"""

from PyQt6.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QWidget, QFrame, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..base_screen import BaseScreen
from ..components.dropdown_menu import DropdownMenu
from ..panels.team_overview_panel import TeamOverviewPanel
from ..panels.player_management_panel import PlayerManagementPanel
from ..panels.match_center_panel import MatchCenterPanel
from ...core.config import (COLOR_PRIMARY, COLOR_SURFACE, COLOR_BORDER,
                           COLOR_TEXT, MAIN_FONT_FAMILY)


class GameDashboardScreen(BaseScreen):
    """Dashboard principale con menu a tendina e area pannelli"""

    panel_changed = pyqtSignal(str)

    def __init__(self, game_data, parent=None):
        self.game_data = game_data
        self.current_panel = None
        self.panels_cache = {}
        super().__init__("", parent)  # Nessun titolo per la dashboard

    def setup_content(self):
        """Configura il contenuto della dashboard"""
        # Layout principale
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Header con menu a tendina
        self.create_menu_header(main_layout)

        # Area centrale per i pannelli
        self.create_panel_area(main_layout)

        # Footer con info rapide
        self.create_footer(main_layout)

        self.add_content_layout(main_layout)

        # Mostra pannello iniziale
        self.show_panel('team_overview')

    def create_menu_header(self, parent_layout):
        """Crea l'header con i menu a tendina"""
        header_frame = QFrame()
        header_frame.setFixedHeight(60)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLOR_SURFACE};
                border-bottom: 2px solid {COLOR_BORDER};
            }}
        """)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)

        # Info club corrente
        club_name = self.game_data.get('selected_club', {}).get('nome', 'Club')
        club_label = QLabel(f"🏆 {club_name}")
        club_label.setFont(QFont(MAIN_FONT_FAMILY, 12, QFont.Weight.Bold))
        club_label.setStyleSheet(f"color: {COLOR_PRIMARY};")
        header_layout.addWidget(club_label)

        header_layout.addStretch()

        # Menu a tendina
        self.create_dropdown_menus(header_layout)

        parent_layout.addWidget(header_frame)

    def create_dropdown_menus(self, parent_layout):
        """Crea i menu a tendina principali"""
        # Menu Squadra
        squadra_menu = DropdownMenu("Squadra")
        squadra_menu.add_action("Panoramica", lambda: self.show_panel('team_overview'))
        squadra_menu.add_action("Rosa Giocatori", lambda: self.show_panel('player_management'))
        squadra_menu.add_action("Tattiche", lambda: self.show_panel('tactics'))
        squadra_menu.add_action("Strutture", lambda: self.show_panel('facilities'))
        parent_layout.addWidget(squadra_menu)

        # Menu Partite
        partite_menu = DropdownMenu("Partite")
        partite_menu.add_action("Centro Partite", lambda: self.show_panel('match_center'))
        partite_menu.add_action("Calendario", lambda: self.show_panel('calendar'))
        partite_menu.add_action("Risultati", lambda: self.show_panel('results'))
        partite_menu.add_action("Classifiche", lambda: self.show_panel('standings'))
        parent_layout.addWidget(partite_menu)

        # Menu Mercato
        mercato_menu = DropdownMenu("Mercato")
        mercato_menu.add_action("Trasferimenti", lambda: self.show_panel('transfers'))
        mercato_menu.add_action("Contratti", lambda: self.show_panel('contracts'))
        mercato_menu.add_action("Prestiti", lambda: self.show_panel('loans'))
        mercato_menu.add_action("Scouting", lambda: self.show_panel('scouting'))
        parent_layout.addWidget(mercato_menu)

        # Menu Gestione
        gestione_menu = DropdownMenu("Gestione")
        gestione_menu.add_action("Finanze", lambda: self.show_panel('finances'))
        gestione_menu.add_action("Statistiche", lambda: self.show_panel('statistics'))
        gestione_menu.add_action("Staff", lambda: self.show_panel('staff'))
        gestione_menu.add_action("Impostazioni", lambda: self.show_panel('settings'))
        parent_layout.addWidget(gestione_menu)

        # Menu Simulazione
        simulation_menu = DropdownMenu("Simulazione")
        simulation_menu.add_action("Controlli", lambda: self.show_panel('simulation'))
        simulation_menu.add_action("Timeline", lambda: self.show_panel('timeline'))
        simulation_menu.add_action("Notizie", lambda: self.show_panel('news'))
        parent_layout.addWidget(simulation_menu)

    def create_panel_area(self, parent_layout):
        """Crea l'area centrale per i pannelli"""
        self.panel_container = QWidget()
        self.panel_container.setStyleSheet(f"""
            QWidget {{
                background-color: {COLOR_SURFACE};
            }}
        """)

        self.panel_layout = QVBoxLayout(self.panel_container)
        self.panel_layout.setContentsMargins(0, 0, 0, 0)

        parent_layout.addWidget(self.panel_container)

    def create_footer(self, parent_layout):
        """Crea il footer con informazioni rapide"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(30)
        footer_frame.setStyleSheet(f"""
            QFrame {{
                background-color: #f8f9fa;
                border-top: 1px solid {COLOR_BORDER};
            }}
        """)

        footer_layout = QHBoxLayout(footer_frame)
        footer_layout.setContentsMargins(20, 5, 20, 5)

        # Info stagione
        season = self.game_data.get('season', '2025/26')
        season_label = QLabel(f"Stagione: {season}")
        footer_layout.addWidget(season_label)

        footer_layout.addStretch()

        # Budget rapido
        budget = self.game_data.get('selected_club', {}).get('budget_trasferimenti_eur', 0)
        budget_label = QLabel(f"Budget: €{budget:,.0f}")
        footer_layout.addWidget(budget_label)

        # Data corrente (placeholder)
        date_label = QLabel("Data: 1 Luglio 2025")
        footer_layout.addWidget(date_label)

        parent_layout.addWidget(footer_frame)

    def show_panel(self, panel_name):
        """Mostra un pannello specifico nell'area centrale"""
        # Rimuovi pannello corrente se presente
        if self.current_panel:
            self.panel_layout.removeWidget(self.current_panel)
            self.current_panel.hide()

        # Crea o recupera pannello dalla cache
        if panel_name not in self.panels_cache:
            self.panels_cache[panel_name] = self.create_panel(panel_name)

        panel = self.panels_cache[panel_name]
        if panel:
            self.panel_layout.addWidget(panel)
            panel.show()
            self.current_panel = panel

            # Aggiorna dati del pannello
            if hasattr(panel, 'set_game_data'):
                panel.set_game_data(self.game_data)

            # Emetti segnale cambio pannello
            self.panel_changed.emit(panel_name)

    def create_panel(self, panel_name):
        """Crea un pannello specifico"""
        panel_classes = {
            'team_overview': TeamOverviewPanel,
            'player_management': PlayerManagementPanel,
            'match_center': MatchCenterPanel,
            'tactics': lambda: self.create_placeholder_panel("Tattiche"),
            'facilities': lambda: self.create_placeholder_panel("Strutture"),
            'calendar': lambda: self.create_placeholder_panel("Calendario"),
            'results': lambda: self.create_placeholder_panel("Risultati"),
            'standings': lambda: self.create_placeholder_panel("Classifiche"),
            'transfers': lambda: self.create_placeholder_panel("Trasferimenti"),
            'contracts': lambda: self.create_placeholder_panel("Contratti"),
            'loans': lambda: self.create_placeholder_panel("Prestiti"),
            'scouting': lambda: self.create_placeholder_panel("Scouting"),
            'finances': lambda: self.create_placeholder_panel("Finanze"),
            'statistics': lambda: self.create_placeholder_panel("Statistiche"),
            'staff': lambda: self.create_placeholder_panel("Staff"),
            'settings': lambda: self.create_placeholder_panel("Impostazioni"),
            'simulation': lambda: self.create_placeholder_panel("Simulazione"),
            'timeline': lambda: self.create_placeholder_panel("Timeline"),
            'news': lambda: self.create_placeholder_panel("Notizie")
        }

        panel_class = panel_classes.get(panel_name)
        if panel_class:
            try:
                return panel_class()
            except Exception as e:
                print(f"Errore creazione pannello {panel_name}: {e}")
                return self.create_placeholder_panel(panel_name.title())

        return None

    def create_placeholder_panel(self, title):
        """Crea un pannello placeholder per funzionalità non implementate"""
        placeholder = QWidget()
        layout = QVBoxLayout(placeholder)

        label = QLabel(f"Pannello {title}")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setFont(QFont(MAIN_FONT_FAMILY, 16, QFont.Weight.Bold))
        label.setStyleSheet("color: #666; margin: 50px;")

        description = QLabel(f"Il pannello {title} sarà implementato nelle prossime versioni.")
        description.setAlignment(Qt.AlignmentFlag.AlignCenter)
        description.setStyleSheet("color: #999; margin: 20px;")

        layout.addStretch()
        layout.addWidget(label)
        layout.addWidget(description)
        layout.addStretch()

        return placeholder

    def refresh_current_panel(self):
        """Aggiorna il pannello corrente"""
        if self.current_panel and hasattr(self.current_panel, 'refresh'):
            self.current_panel.refresh()

    def get_current_panel_name(self):
        """Restituisce il nome del pannello corrente"""
        for name, panel in self.panels_cache.items():
            if panel == self.current_panel:
                return name
        return None
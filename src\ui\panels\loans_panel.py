"""
Pannello gestione prestiti
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QH<PERSON><PERSON>Layout, <PERSON><PERSON>abe<PERSON>,
                            QGroup<PERSON>ox, QTabWidget)
from PyQt6.QtCore import Qt, pyqtSignal

from ..components.data_table import DataTable
from ..components.info_display import InfoDisplay
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class LoansPanel(QWidget):
    """Pannello per gestione prestiti"""

    loan_requested = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        header_layout = QHBoxLayout()
        title_label = QLabel("Gestione Prestiti")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        main_layout.addLayout(header_layout)

        # Tab widget
        self.tab_widget = QTabWidget()

        # Tab Prestiti In Uscita
        out_widget = QWidget()
        out_layout = QVBoxLayout(out_widget)
        
        self.out_loans_table = DataTable([
            {"key": "giocatore", "title": "Giocatore", "width": 150, "stretch": True},
            {"key": "squadra", "title": "Squadra", "width": 120},
            {"key": "scadenza", "title": "Scadenza", "width": 100, "align": "center"},
            {"key": "tipo", "title": "Tipo", "width": 80, "align": "center"}
        ])
        out_layout.addWidget(self.out_loans_table)
        
        self.tab_widget.addTab(out_widget, "📤 In Uscita")

        # Tab Prestiti In Entrata
        in_widget = QWidget()
        in_layout = QVBoxLayout(in_widget)
        
        self.in_loans_table = DataTable([
            {"key": "giocatore", "title": "Giocatore", "width": 150, "stretch": True},
            {"key": "squadra_origine", "title": "Da", "width": 120},
            {"key": "scadenza", "title": "Scadenza", "width": 100, "align": "center"},
            {"key": "opzione", "title": "Opzione", "width": 100, "align": "center"}
        ])
        in_layout.addWidget(self.in_loans_table)
        
        self.tab_widget.addTab(in_widget, "📥 In Entrata")

        main_layout.addWidget(self.tab_widget)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {'type': 'loans'}

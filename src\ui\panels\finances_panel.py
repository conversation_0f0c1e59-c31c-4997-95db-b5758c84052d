"""
Pannello gestione finanze del club
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QGroupBox, QTabWidget, QProgressBar, QGridLayout)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..components.info_display import InfoDisplay
from ..components.data_table import DataTable
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR,
                           MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class FinancesPanel(QWidget):
    """Pannello per gestione finanze"""

    budget_updated = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.financial_data = {}
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        self.create_panel_header(main_layout)

        # Tab widget principale
        self.tab_widget = QTabWidget()

        # Tab Panoramica
        self.create_overview_tab()

        # Tab Budget
        self.create_budget_tab()

        # Tab Transazioni
        self.create_transactions_tab()

        main_layout.addWidget(self.tab_widget)

    def create_panel_header(self, parent_layout):
        """Crea l'header del pannello"""
        header_layout = QHBoxLayout()

        title_label = QLabel("Gestione Finanze")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Bilancio corrente
        self.balance_label = QLabel("Bilancio: €0")
        self.balance_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                color: {COLOR_SUCCESS};
                padding: 5px 10px;
                border: 1px solid {COLOR_SUCCESS};
                border-radius: 4px;
            }}
        """)
        header_layout.addWidget(self.balance_label)

        parent_layout.addLayout(header_layout)

    def create_overview_tab(self):
        """Crea la tab panoramica finanziaria"""
        overview_widget = QWidget()
        layout = QVBoxLayout(overview_widget)

        # Layout a due colonne
        columns_layout = QHBoxLayout()

        # Colonna sinistra - Entrate
        income_group = QGroupBox("Entrate Mensili")
        income_layout = QVBoxLayout(income_group)

        self.income_display = InfoDisplay()
        income_layout.addWidget(self.income_display)

        columns_layout.addWidget(income_group)

        # Colonna destra - Uscite
        expenses_group = QGroupBox("Uscite Mensili")
        expenses_layout = QVBoxLayout(expenses_group)

        self.expenses_display = InfoDisplay()
        expenses_layout.addWidget(self.expenses_display)

        columns_layout.addWidget(expenses_group)

        layout.addLayout(columns_layout)

        # Riepilogo finanziario
        summary_group = QGroupBox("Riepilogo Finanziario")
        summary_layout = QGridLayout(summary_group)

        # Indicatori finanziari
        indicators = [
            ("Liquidità", "€50,000,000", COLOR_SUCCESS),
            ("Debiti", "€20,000,000", COLOR_WARNING),
            ("Fair Play Finanziario", "Conforme", COLOR_SUCCESS),
            ("Rating Creditizio", "A+", COLOR_SUCCESS)
        ]

        for i, (label, value, color) in enumerate(indicators):
            row = i // 2
            col = (i % 2) * 2

            label_widget = QLabel(f"{label}:")
            label_widget.setFont(QFont(MAIN_FONT_FAMILY, 10, QFont.Weight.Bold))
            summary_layout.addWidget(label_widget, row, col)

            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"color: {color}; font-weight: bold;")
            summary_layout.addWidget(value_widget, row, col + 1)

        layout.addWidget(summary_group)

        # Aggiorna dati
        self.update_financial_overview()

        self.tab_widget.addTab(overview_widget, "📊 Panoramica")

    def create_budget_tab(self):
        """Crea la tab gestione budget"""
        budget_widget = QWidget()
        layout = QVBoxLayout(budget_widget)

        # Budget allocations
        allocations_group = QGroupBox("Allocazione Budget")
        allocations_layout = QGridLayout(allocations_group)

        budgets = [
            ("Trasferimenti", 50000000, 100000000),
            ("Stipendi", 30000000, 50000000),
            ("Strutture", 5000000, 20000000),
            ("Giovanile", 2000000, 10000000),
            ("Staff", 3000000, 8000000),
            ("Marketing", 1000000, 5000000)
        ]

        self.budget_bars = {}
        for i, (category, current, maximum) in enumerate(budgets):
            # Nome categoria
            name_label = QLabel(category)
            name_label.setFont(QFont(MAIN_FONT_FAMILY, 10, QFont.Weight.Bold))
            allocations_layout.addWidget(name_label, i, 0)

            # Barra progresso
            progress_bar = QProgressBar()
            progress_bar.setMaximum(maximum)
            progress_bar.setValue(current)
            progress_bar.setFormat(f"€{current:,.0f} / €{maximum:,.0f}")
            progress_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 1px solid {COLOR_BORDER};
                    border-radius: 4px;
                    text-align: center;
                }}
                QProgressBar::chunk {{
                    background-color: {COLOR_PRIMARY};
                    border-radius: 3px;
                }}
            """)
            allocations_layout.addWidget(progress_bar, i, 1)

            # Percentuale utilizzo
            percentage = (current / maximum) * 100
            perc_label = QLabel(f"{percentage:.1f}%")
            perc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            allocations_layout.addWidget(perc_label, i, 2)

            self.budget_bars[category] = progress_bar

        layout.addWidget(allocations_group)

        # Proiezioni
        projections_group = QGroupBox("Proiezioni Stagionali")
        projections_layout = QVBoxLayout(projections_group)

        self.projections_display = InfoDisplay()
        projections_layout.addWidget(self.projections_display)

        layout.addWidget(projections_group)

        # Aggiorna proiezioni
        self.update_projections()

        self.tab_widget.addTab(budget_widget, "💰 Budget")

    def create_transactions_tab(self):
        """Crea la tab transazioni"""
        transactions_widget = QWidget()
        layout = QVBoxLayout(transactions_widget)

        # Tabella transazioni
        columns = [
            {"key": "data", "title": "Data", "width": 100},
            {"key": "tipo", "title": "Tipo", "width": 100},
            {"key": "descrizione", "title": "Descrizione", "width": 200, "stretch": True},
            {"key": "importo", "title": "Importo", "width": 120, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}" if isinstance(x, (int, float)) else "N/A"},
            {"key": "categoria", "title": "Categoria", "width": 100},
            {"key": "saldo", "title": "Saldo", "width": 120, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}" if isinstance(x, (int, float)) else "N/A"}
        ]

        self.transactions_table = DataTable(columns)
        layout.addWidget(self.transactions_table)

        # Carica transazioni di esempio
        self.load_sample_transactions()

        self.tab_widget.addTab(transactions_widget, "📋 Transazioni")

    def update_financial_overview(self):
        """Aggiorna la panoramica finanziaria"""
        # Entrate
        income_data = {
            'Diritti TV': '€15,000,000',
            'Biglietteria': '€8,000,000',
            'Sponsorizzazioni': '€12,000,000',
            'Merchandising': '€3,000,000',
            'Premi UEFA': '€5,000,000'
        }
        self.income_display.set_data(income_data)

        # Uscite
        expenses_data = {
            'Stipendi Giocatori': '€25,000,000',
            'Stipendi Staff': '€3,000,000',
            'Strutture': '€2,000,000',
            'Trasferimenti': '€8,000,000',
            'Operazioni': '€1,500,000'
        }
        self.expenses_display.set_data(expenses_data)

    def update_projections(self):
        """Aggiorna le proiezioni"""
        projections_data = {
            'Entrate Previste': '€45,000,000',
            'Uscite Previste': '€40,000,000',
            'Utile Previsto': '€5,000,000',
            'Break-even': 'Raggiunto',
            'Margine Sicurezza': '€10,000,000'
        }
        self.projections_display.set_data(projections_data)

    def load_sample_transactions(self):
        """Carica transazioni di esempio"""
        sample_transactions = [
            {
                "data": "2025-07-01", "tipo": "Entrata", 
                "descrizione": "Diritti TV - Quota mensile",
                "importo": 1250000, "categoria": "Diritti TV", "saldo": 51250000
            },
            {
                "data": "2025-07-05", "tipo": "Uscita",
                "descrizione": "Stipendi giocatori - Luglio",
                "importo": -2083333, "categoria": "Stipendi", "saldo": 49166667
            },
            {
                "data": "2025-07-10", "tipo": "Entrata",
                "descrizione": "Biglietteria - Amichevole vs Real Madrid",
                "importo": 500000, "categoria": "Biglietteria", "saldo": 49666667
            },
            {
                "data": "2025-07-15", "tipo": "Uscita",
                "descrizione": "Acquisto - Nuovo giocatore",
                "importo": -15000000, "categoria": "Trasferimenti", "saldo": 34666667
            }
        ]
        
        self.transactions_table.set_data(sample_transactions)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data
        club_data = game_data.get('selected_club', {})
        
        # Aggiorna bilancio
        balance = club_data.get('budget_trasferimenti_eur', 0)
        self.balance_label.setText(f"Bilancio: €{balance:,.0f}")

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'finances',
            'financial_data': self.financial_data
        }

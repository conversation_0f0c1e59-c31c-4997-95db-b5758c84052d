"""
Tabella dati riutilizzabile con funzionalità avanzate
"""

from PyQt6.QtWidgets import (QTableWidget, QTableWidgetItem, QHeaderView,
                            QAbstractItemView, QWidget, QVBoxLayout,
                            QHBoxLayout, QLineEdit, QComboBox, QPushButton,
                            QLabel, QCheckBox, QSpinBox)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont

from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_TEXT,
                           COLOR_PRIMARY, MAIN_FONT_FAMILY, MAIN_FONT_SIZE)


class DataTable(QWidget):
    """Tabella dati con filtri, ordinamento e azioni"""

    row_selected = pyqtSignal(int, dict)  # indice, dati riga
    row_double_clicked = pyqtSignal(int, dict)
    data_filtered = pyqtSignal(list)  # dati filtrati

    def __init__(self, columns=None, parent=None):
        super().__init__(parent)
        self.columns = columns or []
        self.original_data = []
        self.filtered_data = []
        self.setup_ui()
        self.setup_style()

    def setup_ui(self):
        """Configura l'interfaccia della tabella"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # Toolbar filtri
        self.create_filter_toolbar(layout)

        # Tabella principale
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.table.setSortingEnabled(True)

        # Configura header
        self.setup_table_headers()

        # Connessioni
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.itemDoubleClicked.connect(self.on_double_click)

        layout.addWidget(self.table)

        # Footer con info
        self.create_footer(layout)

    def create_filter_toolbar(self, parent_layout):
        """Crea la toolbar per i filtri"""
        filter_widget = QWidget()
        filter_layout = QHBoxLayout(filter_widget)
        filter_layout.setContentsMargins(0, 0, 0, 0)

        # Campo ricerca
        self.search_field = QLineEdit()
        self.search_field.setPlaceholderText("Cerca...")
        self.search_field.setFixedWidth(200)
        self.search_field.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(QLabel("Ricerca:"))
        filter_layout.addWidget(self.search_field)

        filter_layout.addStretch()

        # Pulsante reset filtri
        reset_button = QPushButton("Reset")
        reset_button.setFixedSize(60, 25)
        reset_button.clicked.connect(self.reset_filters)
        filter_layout.addWidget(reset_button)

        parent_layout.addWidget(filter_widget)

    def create_footer(self, parent_layout):
        """Crea il footer con informazioni"""
        footer_widget = QWidget()
        footer_layout = QHBoxLayout(footer_widget)
        footer_layout.setContentsMargins(0, 0, 0, 0)

        self.info_label = QLabel("0 elementi")
        footer_layout.addWidget(self.info_label)

        footer_layout.addStretch()

        parent_layout.addWidget(footer_widget)

    def setup_table_headers(self):
        """Configura le intestazioni della tabella"""
        if not self.columns:
            return

        self.table.setColumnCount(len(self.columns))
        headers = []

        for i, col in enumerate(self.columns):
            header_text = col.get('title', col.get('key', f'Col{i}'))
            headers.append(header_text)

            # Configura larghezza colonna
            if 'width' in col:
                self.table.setColumnWidth(i, col['width'])

        self.table.setHorizontalHeaderLabels(headers)

        # Configura ridimensionamento header
        header = self.table.horizontalHeader()
        for i, col in enumerate(self.columns):
            if col.get('stretch', False):
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)
            elif 'width' not in col:
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

    def setup_style(self):
        """Configura lo stile della tabella"""
        self.setStyleSheet(f"""
            QTableWidget {{
                background-color: {COLOR_SURFACE};
                border: 1px solid {COLOR_BORDER};
                border-radius: 6px;
                gridline-color: {COLOR_BORDER};
                font-family: {MAIN_FONT_FAMILY};
                font-size: {MAIN_FONT_SIZE}px;
            }}

            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }}

            QTableWidget::item:selected {{
                background-color: {COLOR_PRIMARY};
                color: white;
            }}

            QTableWidget::item:alternate {{
                background-color: #f8f9fa;
            }}

            QHeaderView::section {{
                background-color: #e9ecef;
                color: {COLOR_TEXT};
                padding: 8px;
                border: none;
                border-right: 1px solid {COLOR_BORDER};
                font-weight: bold;
            }}

            QLineEdit {{
                padding: 5px;
                border: 1px solid {COLOR_BORDER};
                border-radius: 4px;
                font-family: {MAIN_FONT_FAMILY};
                font-size: {MAIN_FONT_SIZE}px;
            }}

            QPushButton {{
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                font-family: {MAIN_FONT_FAMILY};
                font-size: {MAIN_FONT_SIZE}px;
            }}

            QPushButton:hover {{
                background-color: #5a6268;
            }}
        """)

    def set_data(self, data):
        """Imposta i dati della tabella"""
        self.original_data = data.copy() if data else []
        self.apply_filters()

    def apply_filters(self):
        """Applica i filtri ai dati"""
        if not self.original_data:
            self.filtered_data = []
        else:
            search_text = self.search_field.text().lower().strip()

            if not search_text:
                self.filtered_data = self.original_data.copy()
            else:
                self.filtered_data = []
                for item in self.original_data:
                    # Cerca in tutti i campi dell'elemento
                    if self.matches_search(item, search_text):
                        self.filtered_data.append(item)

        self.populate_table()
        self.update_info_label()
        self.data_filtered.emit(self.filtered_data)

    def matches_search(self, item, search_text):
        """Verifica se un elemento corrisponde alla ricerca"""
        if isinstance(item, dict):
            for value in item.values():
                if search_text in str(value).lower():
                    return True
        else:
            if search_text in str(item).lower():
                return True
        return False

    def populate_table(self):
        """Popola la tabella con i dati filtrati"""
        self.table.setRowCount(len(self.filtered_data))

        for row, item in enumerate(self.filtered_data):
            for col, column_def in enumerate(self.columns):
                key = column_def.get('key', '')

                # Ottieni il valore
                if isinstance(item, dict):
                    value = item.get(key, '')
                else:
                    value = getattr(item, key, '') if hasattr(item, key) else ''

                # Applica formatter se presente
                if 'formatter' in column_def:
                    try:
                        value = column_def['formatter'](value)
                    except:
                        pass

                # Crea item tabella
                table_item = QTableWidgetItem(str(value))

                # Applica allineamento
                align = column_def.get('align', 'left')
                if align == 'center':
                    table_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                elif align == 'right':
                    table_item.setTextAlignment(Qt.AlignmentFlag.AlignRight)

                # Imposta item
                self.table.setItem(row, col, table_item)

    def reset_filters(self):
        """Reset tutti i filtri"""
        self.search_field.clear()

    def on_selection_changed(self):
        """Gestisce il cambio di selezione"""
        current_row = self.table.currentRow()
        if 0 <= current_row < len(self.filtered_data):
            self.row_selected.emit(current_row, self.filtered_data[current_row])

    def on_double_click(self, item):
        """Gestisce il doppio click"""
        row = item.row()
        if 0 <= row < len(self.filtered_data):
            self.row_double_clicked.emit(row, self.filtered_data[row])

    def get_selected_data(self):
        """Restituisce i dati della riga selezionata"""
        current_row = self.table.currentRow()
        if 0 <= current_row < len(self.filtered_data):
            return self.filtered_data[current_row]
        return None

    def update_info_label(self):
        """Aggiorna l'etichetta informativa"""
        total = len(self.original_data)
        filtered = len(self.filtered_data)

        if total == filtered:
            text = f"{total} elementi"
        else:
            text = f"{filtered} di {total} elementi"

        self.info_label.setText(text)

    def export_data(self):
        """Esporta i dati filtrati (placeholder)"""
        return self.filtered_data.copy()

    def refresh(self):
        """Aggiorna la visualizzazione"""
        self.apply_filters()
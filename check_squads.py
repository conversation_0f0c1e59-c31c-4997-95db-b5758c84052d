#!/usr/bin/env python3
"""
Controlla i file squadre generati
"""
from src.players.squad_persistence import squad_persistence

print('📁 FILE SQUADRE GENERATI:')
saved_files = squad_persistence.list_saved_squads()

for i, file_info in enumerate(saved_files, 1):
    print(f'\n{i}. {file_info["filename"]}')
    print(f'   📅 Generato: {file_info["generated_at"]}')
    print(f'   ⚽ Squadre: {file_info["total_teams"]}')
    print(f'   👥 Giocatori: {file_info["total_players"]}')
    print(f'   📦 Dimensione: {file_info["file_size_mb"]:.1f} MB')

print(f'\n✅ Sistema di persistenza funzionante!')
print(f'✅ {len(saved_files)} file di squadre disponibili')

if saved_files:
    # Carica il file più recente
    latest_file = saved_files[0]["filename"]
    print(f'\n📂 Caricamento file più recente: {latest_file}')
    
    squads = squad_persistence.load_squads(latest_file)
    if squads:
        summary = squad_persistence.get_squad_summary(squads)
        print(f'\n📊 STATISTICHE COMPLETE:')
        print(f'   🌍 Paesi: {summary["countries"]}')
        print(f'   🏟️ Campionati: {summary["leagues"]}')
        print(f'   ⚽ Squadre: {summary["teams"]}')
        print(f'   👥 Giocatori totali: {summary["total_players"]}')
        
        print(f'\n🔝 Top 5 nazionalità:')
        top_nations = sorted(summary['nationality_distribution'].items(), 
                           key=lambda x: x[1], reverse=True)[:5]
        for nation, count in top_nations:
            percentage = count / summary['total_players'] * 100
            print(f'   {nation}: {count} ({percentage:.1f}%)')
        
        print(f'\n🎯 INTEGRAZIONE COMPLETATA CON SUCCESSO!')
        print(f'🚀 Football Manager Italiano è pronto con il nuovo sistema!')
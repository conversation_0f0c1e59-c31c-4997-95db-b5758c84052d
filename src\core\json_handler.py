"""
JSONHandler - Sistema unificato per la gestione file JSON
Elimina la duplicazione di logiche di caricamento/salvataggio JSON
"""
import json
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """Livelli di validazione JSON"""
    NONE = "none"
    BASIC = "basic"
    STRICT = "strict"


@dataclass
class BackupConfig:
    """Configurazione per backup automatici"""
    enabled: bool = True
    max_backups: int = 5
    backup_dir: Optional[str] = None


@dataclass
class LoadResult:
    """Risultato di un'operazione di caricamento"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    warnings: List[str] = None

    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


@dataclass
class SaveResult:
    """Risultato di un'operazione di salvataggio"""
    success: bool
    backup_created: bool = False
    backup_path: Optional[str] = None
    error: Optional[str] = None


class JSONHandler:
    """
    Handler unificato per operazioni JSON.
    Sostituisce tutte le logiche duplicate di json.load/json.dump
    """

    def __init__(self,
                 encoding: str = 'utf-8',
                 backup_config: Optional[BackupConfig] = None,
                 validation_level: ValidationLevel = ValidationLevel.BASIC):

        self.encoding = encoding
        self.backup_config = backup_config or BackupConfig()
        self.validation_level = validation_level
        self._logger = logging.getLogger(f"{self.__class__.__name__}")

        # Cache per schema validations
        self._schema_cache: Dict[str, Any] = {}

    # === CARICAMENTO SINGOLO FILE ===

    def load_json(self,
                  file_path: Union[str, Path],
                  schema: Optional[Dict[str, Any]] = None,
                  default_value: Any = None,
                  on_error: Optional[Callable[[Exception], Any]] = None) -> LoadResult:
        """
        Carica un file JSON con gestione errori completa

        Args:
            file_path: Percorso del file
            schema: Schema opzionale per validazione
            default_value: Valore di default se file non esiste/errore
            on_error: Callback per gestione errori personalizzata

        Returns:
            LoadResult con dati e informazioni sull'operazione
        """
        path = Path(file_path)
        result = LoadResult(success=False)

        try:
            # Verifica esistenza file
            if not path.exists():
                self._logger.warning(f"File JSON non trovato: {path}")
                if default_value is not None:
                    result.data = default_value
                    result.success = True
                    result.warnings.append(f"File {path.name} non trovato, utilizzato valore default")
                else:
                    result.error = f"File non trovato: {path}"
                return result

            # Verifica dimensioni file
            file_size = path.stat().st_size
            if file_size == 0:
                self._logger.warning(f"File JSON vuoto: {path}")
                result.data = default_value if default_value is not None else {}
                result.success = True
                result.warnings.append(f"File {path.name} vuoto")
                return result

            # Carica il file
            with path.open('r', encoding=self.encoding) as f:
                data = json.load(f)

            # Validazione se richiesta
            if schema and self.validation_level != ValidationLevel.NONE:
                validation_result = self._validate_json_data(data, schema, path.name)
                if not validation_result.success:
                    if self.validation_level == ValidationLevel.STRICT:
                        result.error = f"Validazione fallita: {validation_result.error}"
                        return result
                    else:
                        result.warnings.extend(validation_result.warnings or [])

            result.success = True
            result.data = data

            self._logger.info(f"File JSON caricato: {path} ({file_size} bytes)")

        except json.JSONDecodeError as e:
            error_msg = f"Errore parsing JSON in {path.name}: riga {e.lineno}, colonna {e.colno}"
            self._logger.error(error_msg)
            result.error = error_msg

            if on_error:
                result.data = on_error(e)
                result.success = True

        except (IOError, OSError) as e:
            error_msg = f"Errore I/O leggendo {path}: {str(e)}"
            self._logger.error(error_msg)
            result.error = error_msg

            if on_error:
                result.data = on_error(e)
                result.success = True

        except Exception as e:
            error_msg = f"Errore imprevisto caricando {path}: {str(e)}"
            self._logger.error(error_msg)
            result.error = error_msg

            if on_error:
                result.data = on_error(e)
                result.success = True

        return result

    # === CARICAMENTO BATCH ===

    def load_json_batch(self,
                       file_paths: List[Union[str, Path]],
                       schema: Optional[Dict[str, Any]] = None,
                       stop_on_error: bool = False) -> Dict[str, LoadResult]:
        """
        Carica multipli file JSON in batch

        Args:
            file_paths: Lista percorsi file
            schema: Schema opzionale per validazione
            stop_on_error: Se True, ferma al primo errore

        Returns:
            Dizionario path -> LoadResult
        """
        results = {}

        for file_path in file_paths:
            path_key = str(file_path)
            result = self.load_json(file_path, schema=schema)
            results[path_key] = result

            if stop_on_error and not result.success:
                self._logger.error(f"Batch loading fermato al file: {path_key}")
                break

        successful_loads = sum(1 for r in results.values() if r.success)
        self._logger.info(f"Batch loading completato: {successful_loads}/{len(file_paths)} file caricati")

        return results

    def load_json_from_directory(self,
                                directory: Union[str, Path],
                                pattern: str = "*.json",
                                recursive: bool = False,
                                schema: Optional[Dict[str, Any]] = None) -> Dict[str, LoadResult]:
        """
        Carica tutti i file JSON da una directory

        Args:
            directory: Directory da scansionare
            pattern: Pattern per i file (default *.json)
            recursive: Se True, cerca ricorsivamente
            schema: Schema opzionale per validazione

        Returns:
            Dizionario path -> LoadResult
        """
        dir_path = Path(directory)

        if not dir_path.exists() or not dir_path.is_dir():
            self._logger.error(f"Directory non valida: {dir_path}")
            return {}

        # Trova tutti i file JSON
        if recursive:
            files = list(dir_path.rglob(pattern))
        else:
            files = list(dir_path.glob(pattern))

        self._logger.info(f"Trovati {len(files)} file JSON in {dir_path}")

        return self.load_json_batch(files, schema=schema)

    # === SALVATAGGIO ===

    def save_json(self,
                  data: Any,
                  file_path: Union[str, Path],
                  pretty: bool = True,
                  create_backup: bool = None,
                  atomic: bool = True) -> SaveResult:
        """
        Salva dati in formato JSON

        Args:
            data: Dati da salvare
            file_path: Percorso del file
            pretty: Se True, formatta il JSON con indentazione
            create_backup: Se True, crea backup (default: usa configurazione)
            atomic: Se True, usa scrittura atomica (temp file + rename)

        Returns:
            SaveResult con informazioni sull'operazione
        """
        path = Path(file_path)
        result = SaveResult(success=False)

        if create_backup is None:
            create_backup = self.backup_config.enabled

        try:
            # Crea directory se non esiste
            path.parent.mkdir(parents=True, exist_ok=True)

            # Crea backup se richiesto
            if create_backup and path.exists():
                backup_result = self._create_backup(path)
                result.backup_created = backup_result.success
                result.backup_path = backup_result.backup_path

            # Prepara dati JSON
            json_params = {
                'ensure_ascii': False,
                'separators': (',', ': ') if pretty else (',', ':')
            }
            if pretty:
                json_params['indent'] = 2

            json_content = json.dumps(data, **json_params)

            # Scrittura atomica o diretta
            if atomic:
                temp_path = path.with_suffix(path.suffix + '.tmp')
                with temp_path.open('w', encoding=self.encoding) as f:
                    f.write(json_content)
                    f.flush()

                # Rename atomico
                temp_path.replace(path)
            else:
                with path.open('w', encoding=self.encoding) as f:
                    f.write(json_content)

            result.success = True
            file_size = path.stat().st_size
            self._logger.info(f"File JSON salvato: {path} ({file_size} bytes)")

        except (IOError, OSError) as e:
            result.error = f"Errore I/O salvando {path}: {str(e)}"
            self._logger.error(result.error)

        except (TypeError, ValueError) as e:
            result.error = f"Errore serializzazione JSON: {str(e)}"
            self._logger.error(result.error)

        except Exception as e:
            result.error = f"Errore imprevisto salvando {path}: {str(e)}"
            self._logger.error(result.error)

        return result

    # === BACKUP ===

    def _create_backup(self, file_path: Path) -> SaveResult:
        """Crea backup di un file"""
        result = SaveResult(success=False)

        try:
            # Determina directory backup
            if self.backup_config.backup_dir:
                backup_dir = Path(self.backup_config.backup_dir)
            else:
                backup_dir = file_path.parent / 'backups'

            backup_dir.mkdir(parents=True, exist_ok=True)

            # Nome file backup con timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            backup_path = backup_dir / backup_name

            # Copia file
            shutil.copy2(file_path, backup_path)

            # Gestione max_backups
            self._cleanup_old_backups(backup_dir, file_path.stem)

            result.success = True
            result.backup_path = str(backup_path)
            self._logger.debug(f"Backup creato: {backup_path}")

        except Exception as e:
            result.error = f"Errore creazione backup: {str(e)}"
            self._logger.error(result.error)

        return result

    def _cleanup_old_backups(self, backup_dir: Path, file_stem: str):
        """Pulisce vecchi backup oltre il limite"""
        if self.backup_config.max_backups <= 0:
            return

        # Trova tutti i backup per questo file
        pattern = f"{file_stem}_*.json"
        backups = sorted(backup_dir.glob(pattern), key=lambda p: p.stat().st_mtime, reverse=True)

        # Rimuovi backup eccedenti
        for backup in backups[self.backup_config.max_backups:]:
            try:
                backup.unlink()
                self._logger.debug(f"Backup rimosso: {backup}")
            except Exception as e:
                self._logger.warning(f"Errore rimozione backup {backup}: {e}")

    # === VALIDAZIONE ===

    def _validate_json_data(self, data: Any, schema: Dict[str, Any], filename: str) -> LoadResult:
        """Valida dati JSON contro uno schema"""
        result = LoadResult(success=True)

        try:
            # Validazione base: tipo di dato root
            expected_type = schema.get('type')
            if expected_type:
                if expected_type == 'object' and not isinstance(data, dict):
                    result.success = False
                    result.error = f"Atteso oggetto, trovato {type(data).__name__}"
                    return result
                elif expected_type == 'array' and not isinstance(data, list):
                    result.success = False
                    result.error = f"Atteso array, trovato {type(data).__name__}"
                    return result

            # Validazione campi richiesti (per oggetti)
            if isinstance(data, dict) and 'required' in schema:
                missing_fields = []
                for field in schema['required']:
                    if field not in data:
                        missing_fields.append(field)

                if missing_fields:
                    if self.validation_level == ValidationLevel.STRICT:
                        result.success = False
                        result.error = f"Campi mancanti: {', '.join(missing_fields)}"
                        return result
                    else:
                        result.warnings.append(f"Campi opzionali mancanti in {filename}: {', '.join(missing_fields)}")

            # Validazione struttura proprietà (semplificata)
            if isinstance(data, dict) and 'properties' in schema:
                for prop_name, prop_schema in schema['properties'].items():
                    if prop_name in data:
                        prop_type = prop_schema.get('type')
                        prop_value = data[prop_name]

                        type_valid = self._validate_property_type(prop_value, prop_type)
                        if not type_valid:
                            warning = f"Campo {prop_name} in {filename}: tipo non valido"
                            if self.validation_level == ValidationLevel.STRICT:
                                result.success = False
                                result.error = warning
                                return result
                            else:
                                result.warnings.append(warning)

        except Exception as e:
            result.success = False
            result.error = f"Errore validazione: {str(e)}"

        return result

    def _validate_property_type(self, value: Any, expected_type: str) -> bool:
        """Valida il tipo di una proprietà"""
        if expected_type == 'string':
            return isinstance(value, str)
        elif expected_type == 'number':
            return isinstance(value, (int, float))
        elif expected_type == 'integer':
            return isinstance(value, int)
        elif expected_type == 'boolean':
            return isinstance(value, bool)
        elif expected_type == 'array':
            return isinstance(value, list)
        elif expected_type == 'object':
            return isinstance(value, dict)
        else:
            return True  # Tipo sconosciuto, assume valido

    # === UTILITIES ===

    def merge_json_files(self,
                        file_paths: List[Union[str, Path]],
                        output_path: Union[str, Path],
                        merge_strategy: str = 'update') -> SaveResult:
        """
        Unisce multipli file JSON in uno

        Args:
            file_paths: Lista file da unire
            output_path: File di output
            merge_strategy: 'update' (sovrascrive chiavi) o 'preserve' (mantiene esistenti)

        Returns:
            SaveResult dell'operazione
        """
        merged_data = {}

        # Carica tutti i file
        for file_path in file_paths:
            result = self.load_json(file_path)
            if result.success and isinstance(result.data, dict):
                if merge_strategy == 'update':
                    merged_data.update(result.data)
                elif merge_strategy == 'preserve':
                    for key, value in result.data.items():
                        if key not in merged_data:
                            merged_data[key] = value
            else:
                self._logger.warning(f"Impossibile unire {file_path}: {result.error}")

        # Salva risultato
        return self.save_json(merged_data, output_path)

    def get_json_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Restituisce informazioni su un file JSON senza caricarlo completamente

        Returns:
            Dizionario con informazioni sul file
        """
        path = Path(file_path)
        info = {
            'exists': False,
            'size_bytes': 0,
            'size_human': '0 B',
            'modified': None,
            'is_valid_json': False,
            'estimated_objects': 0
        }

        try:
            if path.exists():
                info['exists'] = True
                stat = path.stat()
                info['size_bytes'] = stat.st_size
                info['size_human'] = self._format_size(stat.st_size)
                info['modified'] = datetime.fromtimestamp(stat.st_mtime)

                # Test veloce validità JSON
                try:
                    with path.open('r', encoding=self.encoding) as f:
                        # Leggi solo i primi caratteri per validazione rapida
                        sample = f.read(100)
                        if sample.strip().startswith(('{', '[')):
                            info['is_valid_json'] = True
                            # Stima oggetti contando le parentesi graffe
                            f.seek(0)
                            full_content = f.read()
                            info['estimated_objects'] = full_content.count('{')
                except:
                    pass

        except Exception as e:
            self._logger.error(f"Errore ottenendo info per {path}: {e}")

        return info

    def _format_size(self, size_bytes: int) -> str:
        """Formatta dimensione file in formato leggibile"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024
        return f"{size_bytes:.1f} TB"


# === FACTORY FUNCTIONS ===

def create_json_handler(config_name: str = 'default') -> JSONHandler:
    """
    Factory per creare JSONHandler con configurazioni predefinite

    Args:
        config_name: Nome configurazione ('default', 'strict', 'backup_disabled')

    Returns:
        JSONHandler configurato
    """
    configs = {
        'default': {
            'validation_level': ValidationLevel.BASIC,
            'backup_config': BackupConfig(enabled=True, max_backups=5)
        },
        'strict': {
            'validation_level': ValidationLevel.STRICT,
            'backup_config': BackupConfig(enabled=True, max_backups=10)
        },
        'backup_disabled': {
            'validation_level': ValidationLevel.BASIC,
            'backup_config': BackupConfig(enabled=False)
        },
        'performance': {
            'validation_level': ValidationLevel.NONE,
            'backup_config': BackupConfig(enabled=False)
        }
    }

    config = configs.get(config_name, configs['default'])
    return JSONHandler(
        validation_level=config['validation_level'],
        backup_config=config['backup_config']
    )


# Istanza globale con configurazione default
json_handler = create_json_handler('default')
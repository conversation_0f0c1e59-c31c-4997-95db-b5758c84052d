"""
Generatore avanzato di squadre basato su reputazione - SISTEMA PRINCIPALE
Utilizza TUTTE le 110 nazioni disponibili per massimo realismo
"""
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple, Set
from enum import Enum
import random

try:
    from .player import Player
    from ..core.utils import logger
    from ..data.data_loader import data_loader
except (ImportError, ValueError):
    try:
        from src.players.player import Player
        from src.core.utils import logger
        from src.data.data_loader import data_loader
    except ImportError:
        from players.player import Player
        from core.utils import logger
        from data.data_loader import data_loader


class TeamTier(Enum):
    """Livelli di squadra basati su reputazione"""
    ELITE = "Elite"      # 18-20 reputazione: Bayern, Real, PSG
    TOP = "Top"          # 15-17 reputazione: Atalanta, Valencia, Dortmund
    GOOD = "Good"        # 12-14 reputazione: Squadre di media classifica
    AVERAGE = "Average"  # 9-11 reputazione: Squadre che lottano per non retrocedere
    WEAK = "Weak"        # 6-8 reputazione: Squadre in difficoltà
    POOR = "Poor"        # 1-5 reputazione: Squadre molto deboli


@dataclass
class TeamProfile:
    """Profilo completo di una squadra"""
    team_name: str
    tier: TeamTier
    reputation: int
    country: str
    league_name: str
    avg_overall_range: Tuple[int, int]
    youth_focus: bool = False
    international_focus: bool = False
    budget_tier: int = 3  # 1=Alto, 2=Medio, 3=Basso
    training_facilities: int = 10  # 1-20


class TeamSquadGenerator:
    """Generatore COMPLETO di rose squadra - SISTEMA AUTONOMO che usa tutte le 110 nazioni"""
    
    def __init__(self):
        # Carica tutte le 110 nazioni disponibili
        self.all_nations = self._load_all_nations()
        logger.info(f"TeamSquadGenerator inizializzato con {len(self.all_nations)} nazioni")
        
        # Coefficienti UEFA per determinare qualità nazionale
        self.uefa_coefficients = self._load_uefa_coefficients()
        
        # Configurazioni tier
        self.tier_configs = self._setup_tier_configurations()
        
        # Cache per profili squadra
        self._team_profiles_cache = {}
    
    def _load_all_nations(self) -> List[str]:
        """Carica TUTTE le 110 nazioni disponibili dal data_loader"""
        if not data_loader.is_loaded():
            data_loader.load_all_data()
        
        # Ottieni tutte le nazioni dal player_names.json
        try:
            # Il data_loader ha già caricato tutti i nomi delle nazioni
            nations = list(data_loader.nation_names.keys()) if hasattr(data_loader, 'nation_names') else []
            if nations:
                logger.info(f"Caricate {len(nations)} nazioni per generazione giocatori")
                return nations
        except:
            pass
        
        # Fallback: ottieni le nazioni dai dati FIFA
        try:
            fifa_data = data_loader.fifa_data
            if fifa_data:
                nations = [country.get('nome', '') for country in fifa_data if country.get('nome')]
                if nations:
                    logger.info(f"Caricate {len(nations)} nazioni da dati FIFA")
                    return nations
        except:
            pass
        
        # Ultimo fallback alle nazioni principali
        fallback_nations = [
            "Italia", "Brasile", "Argentina", "Francia", "Spagna", "Germania", 
            "Inghilterra", "Portogallo", "Olanda", "Uruguay", "Colombia", "Belgio",
            "Croazia", "Danimarca", "Svezia", "Norvegia", "Polonia", "Repubblica Ceca",
            "Ungheria", "Austria", "Svizzera", "Grecia", "Serbia", "Turchia",
            "Russia", "Ucraina", "Romania", "Bulgaria", "Slovacchia", "Slovenia",
            "Finlandia", "Bosnia", "Montenegro", "Albania", "Macedonia", "Kosovo",
            "Lettonia", "Lituania", "Estonia", "Islanda", "Malta", "Cipro",
            "Bielorussia", "Moldavia", "Georgia", "Armenia", "Azerbaigian", "Kazakhstan",
            "Uzbekistan", "Giappone", "Corea del Sud", "Australia", "Iran", "Iraq",
            "Arabia Saudita", "Qatar", "Emirati Arabi", "Marocco", "Algeria", "Tunisia",
            "Egitto", "Nigeria", "Ghana", "Senegal", "Costa d'Avorio", "Camerun",
            "Sudafrica", "USA", "Messico", "Canada", "Panama", "Costa Rica",
            "Honduras", "Jamaica", "Guatemala", "Paraguay", "Ecuador", "Venezuela",
            "Cile", "Bolivia", "Perù"
        ]
        logger.warning(f"Usando nazioni fallback: {len(fallback_nations)} nazioni")
        return fallback_nations
    
    def _load_uefa_coefficients(self) -> Dict[str, float]:
        """Carica coefficienti UEFA per determinare forza nazioni"""
        # Coefficienti basati su ranking FIFA/UEFA (semplificato)
        coefficients = {
            # Top tier (Elite)
            "Brasile": 95, "Argentina": 94, "Francia": 93, "Belgio": 91, "Inghilterra": 90,
            "Italia": 89, "Spagna": 88, "Portogallo": 87, "Olanda": 86, "Germania": 85,
            
            # High tier (Strong)
            "Croazia": 82, "Danimarca": 80, "Uruguay": 78, "Svizzera": 76, "Colombia": 75,
            "Messico": 74, "Svezia": 72, "Polonia": 70, "Austria": 68, "Ucraina": 66,
            
            # Mid tier (Good)
            "Serbia": 64, "Marocco": 62, "Giappone": 60, "Iran": 58, "Turchia": 56,
            "Repubblica Ceca": 54, "Norvegia": 52, "Ungheria": 50, "Russia": 48, "Perù": 46,
            
            # Lower tiers con gradazione
            "Romania": 44, "Slovacchia": 42, "Irlanda": 40, "Bulgaria": 38, "Grecia": 36,
            "Slovenia": 34, "Finlandia": 32, "Bosnia": 30, "Montenegro": 28, "Albania": 26
        }
        
        # Assegna coefficienti a tutte le altre nazioni non specificate
        for nation in self.all_nations:
            if nation not in coefficients:
                # Distribuzione graduale per le nazioni rimanenti
                remaining_nations = [n for n in self.all_nations if n not in coefficients]
                base_coeff = 25
                variation = len(remaining_nations)
                if nation in remaining_nations:
                    nation_index = remaining_nations.index(nation)
                    coefficients[nation] = max(5, base_coeff - (nation_index * 20 // max(1, variation)))
                else:
                    coefficients[nation] = 15  # Default medio
        
        logger.info(f"Coefficienti caricati per {len(coefficients)} nazioni")
        return coefficients
    
    def _setup_tier_configurations(self) -> Dict[TeamTier, Dict]:
        """Configurazioni per ogni tier di squadra"""
        return {
            TeamTier.ELITE: {
                "overall_base": (16, 19),
                "star_players": 3,
                "youth_players": 2,
                "national_ratio": 0.35,  # 35% nazionali per max internazionalità
                "foreign_star_bonus": 2
            },
            TeamTier.TOP: {
                "overall_base": (14, 17),
                "star_players": 2,
                "youth_players": 3,
                "national_ratio": 0.45,
                "foreign_star_bonus": 1
            },
            TeamTier.GOOD: {
                "overall_base": (12, 15),
                "star_players": 1,
                "youth_players": 4,
                "national_ratio": 0.55,
                "foreign_star_bonus": 1
            },
            TeamTier.AVERAGE: {
                "overall_base": (10, 14),
                "star_players": 1,
                "youth_players": 5,
                "national_ratio": 0.70,
                "foreign_star_bonus": 0
            },
            TeamTier.WEAK: {
                "overall_base": (8, 12),
                "star_players": 0,
                "youth_players": 6,
                "national_ratio": 0.80,
                "foreign_star_bonus": 0
            },
            TeamTier.POOR: {
                "overall_base": (6, 10),
                "star_players": 0,
                "youth_players": 8,
                "national_ratio": 0.90,
                "foreign_star_bonus": 0
            }
        }
    
    def analyze_team(self, team_data: Dict, country: str, league_name: str) -> TeamProfile:
        """Analizza una squadra e determina il suo profilo completo"""
        team_name = team_data.get("nome", "Unknown Team")
        reputation = team_data.get("reputazione", 10)
        
        # Determina tier basato su reputazione
        tier = self._determine_team_tier(reputation)
        
        # Calcola range overall medio
        config = self.tier_configs[tier]
        avg_range = config["overall_base"]
        
        profile = TeamProfile(
            team_name=team_name,
            tier=tier,
            reputation=reputation,
            country=country,
            league_name=league_name,
            avg_overall_range=avg_range,
            youth_focus=reputation <= 12,  # Squadre deboli puntano sui giovani
            international_focus=reputation >= 15,  # Squadre forti sono più internazionali
            budget_tier=1 if reputation >= 16 else (2 if reputation >= 12 else 3),
            training_facilities=min(20, max(5, reputation))
        )
        
        return profile
    
    def _determine_team_tier(self, reputation: int) -> TeamTier:
        """Determina il tier della squadra basato sulla reputazione"""
        if reputation >= 18:
            return TeamTier.ELITE
        elif reputation >= 15:
            return TeamTier.TOP
        elif reputation >= 12:
            return TeamTier.GOOD
        elif reputation >= 9:
            return TeamTier.AVERAGE
        elif reputation >= 6:
            return TeamTier.WEAK
        else:
            return TeamTier.POOR
    
    def generate_team_squad(self, profile: TeamProfile, squad_size: int = 25) -> List[Player]:
        """Genera una rosa completa per una squadra usando TUTTE le 110 nazioni"""
        logger.info(f"Generando rosa per {profile.team_name} ({profile.tier.value}, rep: {profile.reputation})")
        
        # Configurazione per il tier
        config = self.tier_configs[profile.tier]
        
        # Determina distribuzione nazionalità
        nationality_dist = self._determine_nationality_distribution(profile)
        
        # Genera formazione base (11 titolari + riserve)
        squad = []
        positions_needed = self._get_position_distribution(squad_size)
        
        for position, count in positions_needed.items():
            for _ in range(count):
                # Determina overall target per questo giocatore
                target_overall = self._determine_player_overall(config, len(squad), squad_size)
                
                # Genera giocatore
                player = self._generate_player(position, target_overall, nationality_dist, profile)
                squad.append(player)
        
        # Statistiche finali
        nations = set(player.nationality for player in squad)
        avg_overall = sum(player.overall_rating for player in squad) / len(squad)
        avg_age = sum(player.age for player in squad) / len(squad)
        national_count = len([p for p in squad if p.nationality == profile.country])
        
        logger.info(f"{profile.team_name}: {len(squad)} giocatori generati")
        logger.info(f"  Overall medio: {avg_overall:.1f}, Età media: {avg_age:.1f}")
        logger.info(f"  Nazionali: {national_count}/{len(squad)} ({national_count/len(squad)*100:.1f}%)")
        
        # Log delle posizioni per debug
        position_counts = {}
        for player in squad:
            pos = player.position
            position_counts[pos] = position_counts.get(pos, 0) + 1
        logger.info(f"  Posizioni: {position_counts}")
        
        return squad
    
    def _get_position_distribution(self, squad_size: int) -> Dict[str, int]:
        """Determina distribuzione posizioni per la rosa"""
        # Formazione base 4-3-3 + riserve
        base_formation = {
            'P': 2,      # 2 portieri
            'DC': 4,     # 4 difensori centrali
            'DD': 2,     # 2 terzini destri  
            'DS': 3,     # 3 terzini sinistri
            'CC': 4,     # 4 centrocampisti centrali
            'CD': 2,     # 2 centrocampisti destri
            'CS': 3,     # 3 centrocampisti sinistri
            'AD': 2,     # 2 attaccanti destri
            'AS': 2,     # 2 attaccanti sinistri
            'C': 1       # 1 centravanti
        }
        
        current_total = sum(base_formation.values())
        
        # Aggiusta per raggiungere squad_size
        if current_total < squad_size:
            # Aggiungi giocatori extra nelle posizioni chiave
            extra_positions = ['CC', 'DC', 'AS', 'AD', 'DS']
            remaining = squad_size - current_total
            
            for i in range(remaining):
                pos = extra_positions[i % len(extra_positions)]
                base_formation[pos] += 1
        elif current_total > squad_size:
            # Riduci giocatori se necessario
            reduction_order = ['CS', 'CD', 'DS', 'AS', 'AD']
            to_reduce = current_total - squad_size
            
            for pos in reduction_order:
                if to_reduce <= 0:
                    break
                if base_formation[pos] > 1:
                    reduction = min(base_formation[pos] - 1, to_reduce)
                    base_formation[pos] -= reduction
                    to_reduce -= reduction
        
        return base_formation
    
    def _determine_player_overall(self, config: Dict, current_squad_size: int, total_squad_size: int) -> int:
        """Determina overall target per un giocatore"""
        min_overall, max_overall = config["overall_base"]
        star_players = config["star_players"]
        
        # Primi giocatori sono le stelle
        if current_squad_size < star_players:
            return random.randint(max_overall, min(20, max_overall + 1))
        
        # Giocatori normali
        return random.randint(min_overall, max_overall)
    
    def _determine_nationality_distribution(self, team_profile: TeamProfile) -> Dict[str, float]:
        """Determina distribuzione nazionalità basata su tier della squadra e TUTTE le 110 nazioni"""
        base_country = team_profile.country if team_profile.country else "Italia"
        config = self.tier_configs[team_profile.tier]
        national_ratio = config["national_ratio"]
        
        # Calcola peso per nazionali
        distribution = {base_country: national_ratio}
        
        # Peso rimanente per stranieri
        foreign_weight = 1.0 - national_ratio
        
        # Seleziona nazioni straniere in base a coefficienti UEFA
        foreign_nations = [n for n in self.all_nations if n != base_country]
        
        # Ordina per coefficiente (migliori prima)
        foreign_nations.sort(key=lambda n: self.uefa_coefficients.get(n, 0), reverse=True)
        
        # Distribuisci peso straniero
        if foreign_nations:
            # Le migliori 20 nazioni prendono più peso
            top_nations = foreign_nations[:min(20, len(foreign_nations))]
            remaining_nations = foreign_nations[20:]
            
            # 70% del peso straniero alle top nazioni
            top_weight = foreign_weight * 0.7
            remaining_weight = foreign_weight * 0.3
            
            # Distribuisci tra top nazioni con pesi decrescenti
            for i, nation in enumerate(top_nations):
                # Peso decrescente: prima nazione ha più peso
                nation_weight = top_weight * (0.3 / (i + 1)) if i < 10 else top_weight * 0.02
                distribution[nation] = nation_weight
            
            # Distribuisci peso rimanente uniformemente
            if remaining_nations:
                uniform_weight = remaining_weight / len(remaining_nations)
                for nation in remaining_nations:
                    distribution[nation] = uniform_weight
        
        # Normalizza per assicurare che la somma sia 1.0
        total_weight = sum(distribution.values())
        if total_weight > 0:
            distribution = {k: v/total_weight for k, v in distribution.items()}
        
        return distribution
    
    def _generate_player_nationality(self, nationality_dist: Dict[str, float]) -> str:
        """Genera nazionalità giocatore basata su distribuzione e tutte le nazioni"""
        # Converti distribuzione in lista pesata
        nations = list(nationality_dist.keys())
        weights = list(nationality_dist.values())
        
        # Normalizza i pesi
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        
        # Selezione pesata
        return random.choices(nations, weights=weights, k=1)[0]
    
    def _generate_player_name(self, nationality: str) -> Tuple[str, str]:
        """Genera nome realistico per nazionalità usando data_loader"""
        try:
            # Debug: log delle nazioni disponibili al primo utilizzo
            if not hasattr(self, '_debug_nations_logged'):
                if hasattr(data_loader, 'nation_names') and data_loader.nation_names:
                    available_nations = list(data_loader.nation_names.keys())
                    logger.info(f"Nazioni disponibili per nomi: {len(available_nations)} - Prime 10: {available_nations[:10]}")
                    self._debug_nations_logged = True

            # Log della nazionalità richiesta per debug
            logger.info(f"Tentativo generazione nome per nazionalità: {nationality}")

            # Accedi ai dati dei nomi direttamente dal data_loader
            if hasattr(data_loader, 'nation_names') and data_loader.nation_names:
                # Prova con il nome esatto della nazionalità
                if nationality in data_loader.nation_names:
                    names_data = data_loader.nation_names[nationality]
                    if isinstance(names_data, dict) and names_data.get('first_names') and names_data.get('last_names'):
                        first_name = random.choice(names_data['first_names'])
                        last_name = random.choice(names_data['last_names'])
                        logger.info(f"Nome generato da JSON per {nationality}: {first_name} {last_name}")
                        return first_name, last_name
                    else:
                        logger.warning(f"Dati non validi per {nationality}: {type(names_data)}")
                else:
                    logger.warning(f"Nazionalità {nationality} non trovata in nation_names")
            else:
                logger.error("data_loader.nation_names non disponibile")

                # Prova con varianti del nome (es. "Germany" -> "Germania")
                name_variants = {
                    'Germany': 'Germania',
                    'Spain': 'Spagna',
                    'France': 'Francia',
                    'Brazil': 'Brasile',
                    'England': 'Inghilterra',
                    'Netherlands': 'Paesi Bassi',
                    'Portugal': 'Portogallo'
                }

                for original, variant in name_variants.items():
                    if nationality == original and variant in data_loader.nation_names:
                        names_data = data_loader.nation_names[variant]
                        if isinstance(names_data, dict) and names_data.get('first_names') and names_data.get('last_names'):
                            first_name = random.choice(names_data['first_names'])
                            last_name = random.choice(names_data['last_names'])
                            return first_name, last_name

                # Se non troviamo la nazionalità esatta, usa i dati italiani come fallback
                if 'Italia' in data_loader.nation_names:
                    names_data = data_loader.nation_names['Italia']
                    if isinstance(names_data, dict) and names_data.get('first_names') and names_data.get('last_names'):
                        first_name = random.choice(names_data['first_names'])
                        last_name = random.choice(names_data['last_names'])
                        return first_name, last_name

        except Exception as e:
            # Log dell'errore per debug
            logger.warning(f"Errore generazione nome per nazionalità {nationality}: {e}")

        # Fallback a nomi generici per nazionalità principali
        fallback_names = {
            "Italia": (["Marco", "Luca", "Andrea", "Alessandro", "Francesco"],
                      ["Rossi", "Bianchi", "Russo", "Romano", "Ferrari"]),
            "Brasile": (["João", "Carlos", "André", "Paulo", "Rafael"],
                       ["Silva", "Santos", "Oliveira", "Pereira", "Costa"]),
            "Argentina": (["Juan", "Diego", "Pablo", "Carlos", "Miguel"],
                         ["González", "Rodríguez", "López", "Martínez", "García"]),
            "Francia": (["Pierre", "Jean", "Michel", "Antoine", "Alexandre"],
                       ["Martin", "Bernard", "Dubois", "Thomas", "Robert"]),
            "Spagna": (["José", "Antonio", "Francisco", "Juan", "Manuel"],
                      ["García", "Rodríguez", "González", "Fernández", "López"]),
            "Germania": (["Hans", "Klaus", "Wolfgang", "Jürgen", "Michael"],
                        ["Müller", "Schmidt", "Schneider", "Fischer", "Weber"])
        }

        if nationality in fallback_names:
            first_names, last_names = fallback_names[nationality]
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            logger.info(f"Nome generato da fallback per {nationality}: {first_name} {last_name}")
            return first_name, last_name

        # Fallback finale a nomi generici italiani
        generic_first = ["Marco", "Luca", "Andrea", "Alessandro", "Francesco", "Giovanni", "Roberto", "Antonio"]
        generic_last = ["Rossi", "Bianchi", "Russo", "Romano", "Ferrari", "Esposito", "Colombo", "Ricci"]

        first_name = random.choice(generic_first)
        last_name = random.choice(generic_last)
        logger.warning(f"Nome generato da fallback finale ITALIANO per {nationality}: {first_name} {last_name}")
        return first_name, last_name
    
    def _generate_player(self, position: str, target_overall: int, nationality_dist: Dict[str, float],
                        team_profile: TeamProfile) -> Player:
        """Genera un singolo giocatore COMPLETO con nazionalità da tutte le 110 nazioni"""
        # Genera nazionalità
        nationality = self._generate_player_nationality(nationality_dist)

        # Genera nome realistico
        first_name, last_name = self._generate_player_name(nationality)

        # Genera età appropriata per il livello
        age = self._generate_realistic_age(target_overall, team_profile.tier)

        # Converti posizione stringa in enum
        from .player import PlayerPosition
        position_mapping = {
            'P': PlayerPosition.PORTIERE,
            'DC': PlayerPosition.DIFENSORE_CENTRALE,
            'DD': PlayerPosition.DIFENSORE_DESTRO,
            'DS': PlayerPosition.DIFENSORE_SINISTRO,
            'CC': PlayerPosition.CENTROCAMPISTA_CENTRALE,
            'CD': PlayerPosition.CENTROCAMPISTA_DESTRO,
            'CS': PlayerPosition.CENTROCAMPISTA_SINISTRO,
            'AD': PlayerPosition.ATTACCANTE_DESTRO,
            'AS': PlayerPosition.ATTACCANTE_SINISTRO,
            'C': PlayerPosition.CENTRAVANTI
        }
        player_position = position_mapping.get(position, PlayerPosition.CENTROCAMPISTA_CENTRALE)

        # Genera data di nascita da età
        import datetime
        current_year = datetime.date.today().year
        birth_year = current_year - age
        birth_date = datetime.date(birth_year, 1, 1)

        # Crea giocatore
        player = Player(
            first_name=first_name,
            last_name=last_name,
            nationality=nationality,
            birth_date=birth_date,
            position=player_position
        )

        # Genera attributi realistici
        self._generate_realistic_attributes(player, target_overall, position)

        return player
    
    def _generate_realistic_age(self, target_overall: int, tier: TeamTier) -> int:
        """Genera età realistica basata su overall e tier squadra"""
        if target_overall >= 16:  # Stelle
            return random.randint(24, 30)
        elif target_overall >= 14:  # Buoni giocatori
            return random.randint(22, 28)
        elif target_overall >= 12:  # Giocatori medi
            return random.randint(20, 32)
        elif target_overall >= 10:  # Giovani/veterani
            age_choice = random.choice(['young', 'veteran'])
            if age_choice == 'young':
                return random.randint(16, 21)
            else:
                return random.randint(32, 36)
        else:  # Giocatori deboli
            return random.randint(18, 34)
    
    def _generate_realistic_attributes(self, player: Player, target_overall: int, position: str):
        """Genera attributi realistici per posizione"""
        # Genera attributi usando la classe PlayerAttributes
        attrs = player.attributes

        # Base attributi
        base_attrs = {
            'velocita': random.randint(max(1, target_overall-3), min(20, target_overall+3)),
            'tiro': random.randint(max(1, target_overall-3), min(20, target_overall+3)),
            'passaggio': random.randint(max(1, target_overall-3), min(20, target_overall+3)),
            'difesa': random.randint(max(1, target_overall-3), min(20, target_overall+3)),
            'forza': random.randint(max(1, target_overall-3), min(20, target_overall+3)),
            'tecnica': random.randint(max(1, target_overall-3), min(20, target_overall+3)),
            'dribbling': random.randint(max(1, target_overall-3), min(20, target_overall+3)),
            'resistenza': random.randint(max(1, target_overall-3), min(20, target_overall+3)),
            'mentalita': random.randint(max(1, target_overall-3), min(20, target_overall+3)),
            'portiere': 1  # Default basso
        }

        # Specializzazioni per posizione
        if position == 'P':  # Portiere
            base_attrs['portiere'] = max(15, random.randint(target_overall, min(20, target_overall+2)))
            base_attrs['tiro'] = max(1, random.randint(1, 5))  # Portieri non tirano
        elif position in ['AD', 'AS']:  # Attaccanti
            base_attrs['tiro'] = max(15, random.randint(target_overall, min(20, target_overall+2)))
            base_attrs['velocita'] = max(12, random.randint(target_overall-2, min(20, target_overall+2)))
            base_attrs['dribbling'] = max(12, random.randint(target_overall-2, min(20, target_overall+2)))
        elif position in ['CC']:  # Centrocampisti centrali
            base_attrs['passaggio'] = max(12, random.randint(target_overall, min(20, target_overall+2)))
            base_attrs['tecnica'] = max(12, random.randint(target_overall-1, min(20, target_overall+2)))
        elif position == 'C':  # Centravanti
            base_attrs['tiro'] = max(15, random.randint(target_overall, min(20, target_overall+2)))
            base_attrs['forza'] = max(12, random.randint(target_overall-1, min(20, target_overall+2)))
        elif position in ['DC', 'DD', 'DS']:  # Difensori
            base_attrs['difesa'] = max(12, random.randint(target_overall, min(20, target_overall+2)))
            base_attrs['forza'] = max(10, random.randint(target_overall-1, min(20, target_overall+2)))

        # Applica attributi agli oggetti PlayerAttributes
        for attr, value in base_attrs.items():
            if hasattr(attrs, attr):
                setattr(attrs, attr, max(1, min(20, value)))


    def generate_complete_squad(self, team_name: str, league_name: str, country: str) -> List[Player]:
        """Genera una rosa completa per una squadra specifica"""
        try:
            # Crea un profilo squadra basato sui parametri
            # Determina tier e reputazione
            tier_int = self._determine_tier_from_league(league_name)
            reputation = self._determine_reputation_from_country_and_league(country, league_name)

            # Converte tier da int a TeamTier enum
            tier_map = {1: TeamTier.TOP, 2: TeamTier.GOOD, 3: TeamTier.AVERAGE, 4: TeamTier.WEAK, 5: TeamTier.POOR}
            tier_enum = tier_map.get(tier_int, TeamTier.AVERAGE)

            profile = TeamProfile(
                team_name=team_name,
                tier=tier_enum,
                reputation=reputation,
                country=country,
                league_name=league_name,
                avg_overall_range=(50, 70)  # Default range
            )

            # Genera la squadra
            squad = self.generate_team_squad(profile, squad_size=25)

            logger.debug(f"Generata squadra {team_name} ({country}, {league_name}): {len(squad)} giocatori")
            return squad

        except Exception as e:
            logger.error(f"Errore generazione squadra {team_name}: {e}")
            return []

    def _determine_tier_from_league(self, league_name: str) -> int:
        """Determina il tier della lega dal nome"""
        league_lower = league_name.lower()

        # Serie A, Premier League, LaLiga, Bundesliga, Ligue 1, etc.
        if any(top_league in league_lower for top_league in [
            'serie a', 'premier', 'laliga', 'bundesliga', 'ligue 1', 'primeira liga',
            'eredivisie', 'jupiler pro', 'super league', 'allsvenskan'
        ]):
            return 1

        # Serie B, Championship, LaLiga 2, 2. Bundesliga, Ligue 2, etc.
        elif any(second_league in league_lower for second_league in [
            'serie b', 'championship', 'laliga 2', '2. bundesliga', 'ligue 2',
            'liga portugal 2', 'eerste divisie', 'challenger pro'
        ]):
            return 2

        # Serie C, League One, etc.
        elif any(third_league in league_lower for third_league in [
            'serie c', 'league one', 'third'
        ]):
            return 3

        # Default per leghe sconosciute
        return 2

    def _determine_reputation_from_country_and_league(self, country: str, league_name: str) -> float:
        """Determina la reputazione basata su paese e lega"""
        country_reputation = {
            'Italia': 8.5, 'Inghilterra': 9.0, 'Spagna': 8.8, 'Germania': 8.7, 'Francia': 8.2,
            'Portogallo': 7.0, 'Paesi Bassi': 7.5, 'Belgio': 6.5, 'Austria': 6.0, 'Turchia': 6.2,
            'Repubblica Ceca': 5.5, 'Scozia': 5.0, 'Svizzera': 6.8, 'Norvegia': 4.5,
            'Danimarca': 6.0, 'Serbia': 5.2, 'Ucraina': 5.8, 'Croazia': 6.5, 'Grecia': 5.0,
            'Israele': 4.0, 'Cipro': 3.5, 'Svezia': 5.5, 'Polonia': 5.8, 'Ungheria': 4.5,
            'Romania': 4.8, 'Bulgaria': 4.0, 'Slovacchia': 4.2, 'Azerbaigian': 3.0,
            'Kazakhstan': 2.5, 'Slovenia': 4.0
        }

        base_reputation = country_reputation.get(country, 4.0)

        # Modifica basata sul tier della lega
        tier = self._determine_tier_from_league(league_name)
        if tier == 1:
            return base_reputation
        elif tier == 2:
            return base_reputation - 1.0
        else:
            return base_reputation - 2.0

    def save_team_data(self, team_name: str, squad: List[Player]):
        """Salva i dati della squadra nel sistema (placeholder)"""
        # Per ora, semplicemente memorizza nella cache globale
        # In futuro potrebbe salvare in database o file
        if not hasattr(self, '_teams_cache'):
            self._teams_cache = {}

        self._teams_cache[team_name] = squad
        logger.debug(f"Salvata squadra {team_name} con {len(squad)} giocatori")

    def get_team_squad(self, team_name: str) -> List[Player]:
        """Ottieni la rosa di una squadra"""
        if hasattr(self, '_teams_cache') and team_name in self._teams_cache:
            return self._teams_cache[team_name]
        return []


# Istanza globale
team_squad_generator = TeamSquadGenerator()
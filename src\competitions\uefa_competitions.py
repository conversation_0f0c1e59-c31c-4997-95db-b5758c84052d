"""
Competizioni UEFA - Champions League, Europa League, Conference League
Sistema League Phase + Playoff nuovo formato 2025/26
"""
from typing import List, Dict, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
import random
from datetime import date, datetime

from ..core.utils import logger
from .cup_competitions import CupCompetition, CupType, CupRound
from .season import Season
from .calendar import Match
from .match_engine import match_simulator

class UEFACompetition(Enum):
    """Competizioni UEFA"""
    CHAMPIONS_LEAGUE = "UEFA Champions League"
    EUROPA_LEAGUE = "UEFA Europa League"
    CONFERENCE_LEAGUE = "UEFA Conference League"

class UEFAPhase(Enum):
    """Fasi competizioni UEFA"""
    LEAGUE_PHASE = "League Phase"
    PLAYOFF_ROUND = "Playoff Round"
    ROUND_16 = "Round of 16"
    QUARTER_FINALS = "Quarter-finals"
    SEMI_FINALS = "Semi-finals"
    FINAL = "Final"

@dataclass
class UEFALeagueMatch(Match):
    """Partita League Phase UEFA"""
    home_coefficient: float = 0.0
    away_coefficient: float = 0.0

@dataclass
class LeaguePhaseStanding:
    """Classifica League Phase"""
    position: int
    team_name: str
    country: str
    matches_played: int = 0
    wins: int = 0
    draws: int = 0
    losses: int = 0
    goals_for: int = 0
    goals_against: int = 0
    points: int = 0

    @property
    def goal_difference(self) -> int:
        return self.goals_for - self.goals_against

    def update_from_match(self, goals_for: int, goals_against: int):
        """Aggiorna da risultato partita"""
        self.matches_played += 1
        self.goals_for += goals_for
        self.goals_against += goals_against

        if goals_for > goals_against:
            self.wins += 1
            self.points += 3
        elif goals_for == goals_against:
            self.draws += 1
            self.points += 1
        else:
            self.losses += 1

class UEFACompetitionManager(CupCompetition):
    """Gestione competizione UEFA con League Phase"""

    def __init__(self, name: str, season: Season, competition_type: UEFACompetition, teams_count: int):
        super().__init__(name, season, CupType.COPPA_ITALIA)  # Usa CupType esistente
        self.uefa_competition = competition_type
        self.teams_count = teams_count
        self.league_phase_matches = self._get_league_phase_matches_count()

        # League Phase standings
        self.league_standings: Dict[str, LeaguePhaseStanding] = {}
        self.current_matchday = 0
        self.max_matchdays = 8 if competition_type != UEFACompetition.CONFERENCE_LEAGUE else 6

        # Dati squadre per simulazione realistica
        self.uefa_teams_data: Optional[Dict] = None

        logger.info(f"Creata competizione UEFA: {name} ({teams_count} squadre, {self.max_matchdays} giornate)")

    def _get_league_phase_matches_count(self) -> int:
        """Calcola numero partite League Phase per squadra"""
        if self.uefa_competition == UEFACompetition.CONFERENCE_LEAGUE:
            return 6  # Conference League: 6 partite
        else:
            return 8  # Champions League e Europa League: 8 partite

    def setup_qualified_teams(self, qualified_teams: List[Dict]):
        """Setup squadre qualificate per la competizione"""
        self.teams = []
        self.league_standings = {}

        for i, team_data in enumerate(qualified_teams[:self.teams_count]):
            team_name = team_data["nome"]
            country = team_data.get("country", "Unknown")

            self.teams.append(team_name)
            self.league_standings[team_name] = LeaguePhaseStanding(
                position=i + 1,
                team_name=team_name,
                country=country
            )

        logger.info(f"{self.name}: Setup completato con {len(self.teams)} squadre")

    def set_uefa_teams_data(self, uefa_teams_data: Dict):
        """Imposta dati squadre UEFA per simulazione realistica"""
        self.uefa_teams_data = uefa_teams_data

        # Conta quante squadre della competizione hanno effettivamente i dati
        teams_with_data = sum(1 for team in self.teams if team in uefa_teams_data)

        logger.debug(f"{self.name}: Impostati dati UEFA per {teams_with_data}/{len(self.teams)} squadre")

    def get_uefa_team_players(self, team_name: str) -> List:
        """Restituisce giocatori squadra UEFA"""
        from ..players.team_squad_generator import team_squad_generator

        # Prima controlla la cache del generatore
        squad = team_squad_generator.get_team_squad(team_name)
        if squad and len(squad) >= 11:
            return squad

        # Fallback ai dati UEFA legacy se esistono
        if self.uefa_teams_data:
            # Cerca prima con nome diretto, poi con prefisso EUR_
            if team_name in self.uefa_teams_data:
                return self.uefa_teams_data[team_name]

            eur_key = f"EUR_{team_name}"
            if eur_key in self.uefa_teams_data:
                return self.uefa_teams_data[eur_key]

        logger.warning(f"Dati squadra non trovati per {team_name} (né in cache né in UEFA data)")
        return []

    def generate_league_phase_fixtures(self) -> List[UEFALeagueMatch]:
        """Genera fixture League Phase (algoritmo Swiss)"""
        fixtures = []

        if len(self.teams) < self.teams_count:
            logger.warning(f"Squadre insufficienti per {self.name}: {len(self.teams)}/{self.teams_count}")
            return fixtures

        # Algoritmo semplificato per generare fixture Swiss-style
        # Ogni squadra gioca contro 8 (o 6 per Conference) avversarie diverse
        teams_copy = self.teams.copy()

        for matchday in range(1, self.max_matchdays + 1):
            matchday_fixtures = []
            random.shuffle(teams_copy)  # Randomizza per ogni giornata

            # Crea coppie per questa giornata
            for i in range(0, len(teams_copy), 2):
                if i + 1 < len(teams_copy):
                    home_team = teams_copy[i]
                    away_team = teams_copy[i + 1]

                    # Crea match
                    match = UEFALeagueMatch(
                        matchday=matchday,
                        home_team=home_team,
                        away_team=away_team,
                        date=date.today(),  # Placeholder, sarà assegnata dal calendario
                        time="20:45",  # Orario standard UEFA
                        competition=self.name,
                        venue=f"Stadio {home_team}"
                    )

                    matchday_fixtures.append(match)
                    fixtures.append(match)

            logger.debug(f"{self.name} MD{matchday}: {len(matchday_fixtures)} partite")

        logger.info(f"{self.name}: Generati {len(fixtures)} fixture League Phase")
        return fixtures

    def simulate_match(self, match: UEFALeagueMatch) -> Dict:
        """Simula una singola partita UEFA usando MatchSimulator unificato"""
        if match.played:
            return {"error": "Partita già giocata"}

        # Trova la partita nel nostro sistema
        uefa_match = None
        for m in self.matches:
            if (m.home_team == match.home_team and
                m.away_team == match.away_team and
                m.date == match.date):
                uefa_match = m
                break

        if not uefa_match:
            return {"error": "Partita UEFA non trovata"}

        # Ottieni giocatori delle squadre
        home_players = self.get_uefa_team_players(match.home_team)
        away_players = self.get_uefa_team_players(match.away_team)

        # Usa MatchSimulator unificato che gestisce automaticamente simulazione con/senza giocatori
        result = match_simulator.simulate_match(uefa_match, home_players, away_players)

        # Aggiorna classifiche UEFA solo se non ci sono errori
        if "error" not in result:
            self._update_league_standings(uefa_match)

        return result

    def simulate_league_phase_matchday(self, matchday: int) -> List[Dict]:
        """Simula giornata League Phase"""
        if matchday > self.max_matchdays:
            return []

        results = []
        matchday_fixtures = [f for f in self.matches if getattr(f, 'matchday', 0) == matchday]

        for match in matchday_fixtures:
            if match.played:
                continue

            # Ottieni giocatori delle squadre UEFA
            home_players = self.get_uefa_team_players(match.home_team)
            away_players = self.get_uefa_team_players(match.away_team)

            # Usa MatchSimulator unificato che gestisce automaticamente simulazione con/senza giocatori
            result = match_simulator.simulate_match(match, home_players, away_players)

            # Aggiorna classifiche UEFA solo se non ci sono errori
            if "error" not in result:
                self._update_league_standings(match)

                # Aggiorna result per formato UEFA
                result["match_id"] = f"{match.home_team}_vs_{match.away_team}_MD{matchday}"
                result["competition"] = self.name

                results.append(result)

        if results:
            self.current_matchday = matchday
            logger.info(f"{self.name} MD{matchday}: {len(results)} partite simulate")

            # Controlla se la League Phase è completata e può procedere al sorteggio
            if self.is_league_phase_completed():
                new_matches = self.progress_to_next_round()
                if new_matches:
                    logger.info(f"{self.name}: New knockout matches scheduled after League Phase completion")

        return results

    def _update_league_standings(self, match: UEFALeagueMatch):
        """Aggiorna classifica League Phase"""
        home_standing = self.league_standings.get(match.home_team)
        away_standing = self.league_standings.get(match.away_team)

        if home_standing:
            home_standing.update_from_match(match.home_score, match.away_score)

        if away_standing:
            away_standing.update_from_match(match.away_score, match.home_score)

        # Riordina classifica
        self._sort_league_standings()

    def _sort_league_standings(self):
        """Ordina classifica League Phase"""
        sorted_standings = sorted(
            self.league_standings.values(),
            key=lambda s: (-s.points, -s.goal_difference, -s.goals_for, s.team_name)
        )

        for i, standing in enumerate(sorted_standings):
            standing.position = i + 1

    def get_league_standings(self) -> List[LeaguePhaseStanding]:
        """Restituisce classifica League Phase"""
        return sorted(self.league_standings.values(), key=lambda s: s.position)

    def get_qualified_for_round16(self) -> List[str]:
        """Restituisce squadre qualificate per Round of 16 (posizioni 1-8)"""
        standings = self.get_league_standings()
        return [s.team_name for s in standings[:8]]

    def get_playoff_teams(self) -> List[str]:
        """Restituisce squadre per playoff (posizioni 9-24)"""
        standings = self.get_league_standings()
        return [s.team_name for s in standings[8:24]]

    def get_eliminated_teams(self) -> List[str]:
        """Restituisce squadre eliminate (posizioni 25-36)"""
        standings = self.get_league_standings()
        return [s.team_name for s in standings[24:]]

    def is_league_phase_completed(self) -> bool:
        """Verifica se League Phase è completata"""
        return self.current_matchday >= self.max_matchdays

    def _league_phase_actually_started(self) -> bool:
        """Verifica se la League Phase è effettivamente iniziata (almeno una partita giocata)"""
        for match in self.matches:
            if getattr(match, 'played', False):
                return True
        return False

    def get_played_matches(self) -> List[UEFALeagueMatch]:
        """Restituisce partite già giocate"""
        return [m for m in self.matches if m.played]

    def get_upcoming_matches(self, limit: Optional[int] = None) -> List[UEFALeagueMatch]:
        """Restituisce prossime partite da giocare"""
        upcoming = [m for m in self.matches if not m.played]
        upcoming.sort(key=lambda m: (m.date, m.time))
        return upcoming[:limit] if limit else upcoming

    def get_matches_for_matchday(self, matchday: int) -> List[UEFALeagueMatch]:
        """Restituisce partite di una specifica giornata"""
        return [m for m in self.matches if m.matchday == matchday]

    def get_competition_stats(self) -> Dict:
        """Statistiche competizione"""
        total_matches = len([m for m in self.matches if hasattr(m, 'played') and m.played])

        return {
            "name": self.name,
            "type": "UEFA Competition",
            "current_phase": self.get_current_phase(),
            "current_matchday": self.current_matchday,
            "max_matchdays": self.max_matchdays,
            "teams_count": len(self.teams),
            "matches_played": total_matches,
            "league_phase_completed": self.is_league_phase_completed(),
            "qualified_r16": len(self.get_qualified_for_round16()) if self.is_league_phase_completed() else 0,
            "playoff_teams": len(self.get_playoff_teams()) if self.is_league_phase_completed() else 0,
            "eliminated": len(self.get_eliminated_teams()) if self.is_league_phase_completed() else 0,
            "can_draw_next_round": self.can_draw_next_round()
        }

    def get_current_phase(self) -> str:
        """Restituisce la fase attuale della competizione"""
        if not self.is_league_phase_completed():
            return UEFAPhase.LEAGUE_PHASE.value
        elif hasattr(self, 'current_knockout_phase'):
            return self.current_knockout_phase
        else:
            return "Waiting for Playoff Draw"

    def can_draw_next_round(self) -> bool:
        """Verifica se è possibile sorteggiare il prossimo turno"""
        if not self.is_league_phase_completed():
            return False

        # IMPORTANTE: Richiede che la League Phase sia effettivamente iniziata
        if not self._league_phase_actually_started():
            return False

        # Se non abbiamo ancora creato i playoff, possiamo crearli
        if not hasattr(self, 'playoff_matches'):
            return True

        # Se i playoff sono completati, possiamo sorteggiare gli ottavi
        if hasattr(self, 'playoff_matches') and self.are_playoff_completed():
            return not hasattr(self, 'round16_matches')

        return False

    def are_playoff_completed(self) -> bool:
        """Verifica se i playoff sono completati"""
        if not hasattr(self, 'playoff_matches'):
            return False
        return all(m.played for m in self.playoff_matches)

    def create_playoff_round(self) -> List[Match]:
        """Crea il turno playoff dopo il completamento della League Phase"""
        if not self.is_league_phase_completed():
            logger.warning(f"{self.name}: Cannot create playoff - League Phase not completed")
            return []

        # Ottieni squadre qualificate per playoff (9°-24°)
        playoff_teams = self.get_playoff_teams()

        if len(playoff_teams) < 16:
            logger.warning(f"{self.name}: Not enough playoff teams: {len(playoff_teams)}")
            return []

        # Sorteggio playoff: 9°-16° vs 17°-24°
        seeded_teams = playoff_teams[:8]  # 9°-16° (teste di serie)
        unseeded_teams = playoff_teams[8:16]  # 17°-24° (non teste di serie)

        random.shuffle(seeded_teams)
        random.shuffle(unseeded_teams)

        playoff_matches = []

        for i in range(8):
            # Andata
            match_leg1 = Match(
                home_team=unseeded_teams[i],  # Non teste di serie in casa andata
                away_team=seeded_teams[i],    # Teste di serie in trasferta andata
                competition=self.name,
                venue=f"Stadio {unseeded_teams[i]}",
                is_knockout=True,
                knockout_round="Playoff Round - Leg 1"
            )

            # Ritorno
            match_leg2 = Match(
                home_team=seeded_teams[i],    # Teste di serie in casa ritorno
                away_team=unseeded_teams[i],  # Non teste di serie in trasferta ritorno
                competition=self.name,
                venue=f"Stadio {seeded_teams[i]}",
                is_knockout=True,
                knockout_round="Playoff Round - Leg 2"
            )

            playoff_matches.extend([match_leg1, match_leg2])

        self.playoff_matches = playoff_matches
        self.current_knockout_phase = UEFAPhase.PLAYOFF_ROUND.value

        logger.info(f"{self.name}: Created playoff round with {len(playoff_matches)} matches (16 teams)")
        return playoff_matches

    def get_playoff_winners(self) -> List[str]:
        """Ottieni vincitori dei playoff (solo dopo che sono completati)"""
        if not self.are_playoff_completed():
            return []

        winners = []

        # Raggruppa per coppia (andata e ritorno)
        for i in range(0, len(self.playoff_matches), 2):
            leg1 = self.playoff_matches[i]
            leg2 = self.playoff_matches[i + 1]

            # Calcola risultato aggregato
            team1_aggregate = leg1.home_score + leg2.away_score
            team2_aggregate = leg1.away_score + leg2.home_score

            if team1_aggregate > team2_aggregate:
                winners.append(leg1.home_team)
            elif team2_aggregate > team1_aggregate:
                winners.append(leg1.away_team)
            else:
                # In caso di pareggio, vince chi ha segnato più gol in trasferta
                team1_away_goals = leg2.away_score
                team2_away_goals = leg1.away_score

                if team1_away_goals > team2_away_goals:
                    winners.append(leg1.home_team)
                else:
                    winners.append(leg1.away_team)

        return winners

    def create_round16(self) -> List[Match]:
        """Crea ottavi di finale dopo playoff completati"""
        if not self.are_playoff_completed():
            logger.warning(f"{self.name}: Cannot create Round of 16 - Playoffs not completed")
            return []

        # Squadre qualificate: prime 8 dalla League Phase + 8 vincitori playoff
        direct_qualified = self.get_qualified_for_round16()
        playoff_winners = self.get_playoff_winners()

        round16_teams = direct_qualified + playoff_winners

        if len(round16_teams) != 16:
            logger.warning(f"{self.name}: Wrong number of R16 teams: {len(round16_teams)}")
            return []

        # Sorteggio ottavi (semplificato)
        random.shuffle(round16_teams)

        round16_matches = []

        for i in range(0, 16, 2):
            # Andata
            match_leg1 = Match(
                home_team=round16_teams[i],
                away_team=round16_teams[i + 1],
                competition=self.name,
                venue=f"Stadio {round16_teams[i]}",
                is_knockout=True,
                knockout_round="Round of 16 - Leg 1"
            )

            # Ritorno
            match_leg2 = Match(
                home_team=round16_teams[i + 1],
                away_team=round16_teams[i],
                competition=self.name,
                venue=f"Stadio {round16_teams[i + 1]}",
                is_knockout=True,
                knockout_round="Round of 16 - Leg 2"
            )

            round16_matches.extend([match_leg1, match_leg2])

        self.round16_matches = round16_matches
        self.current_knockout_phase = UEFAPhase.ROUND_16.value

        logger.info(f"{self.name}: Created Round of 16 with {len(round16_matches)} matches")
        return round16_matches

    def progress_to_next_round(self):
        """Gestisce la progressione automatica al turno successivo quando possibile"""
        # IMPORTANTE: Non generare knockout durante setup iniziale
        if not self._league_phase_actually_started():
            logger.debug(f"{self.name}: Saltando progressione automatica - League Phase non ancora iniziata")
            return []

        if self.can_draw_next_round():
            if not hasattr(self, 'playoff_matches'):
                # Crea playoff
                new_matches = self.create_playoff_round()
                if new_matches:
                    # Aggiungi le partite al calendario principale
                    self.matches.extend(new_matches)
                    logger.info(f"{self.name}: Automatically progressed to Playoff Round")
                    return new_matches
            elif self.are_playoff_completed() and not hasattr(self, 'round16_matches'):
                # Crea ottavi
                new_matches = self.create_round16()
                if new_matches:
                    # Aggiungi le partite al calendario principale
                    self.matches.extend(new_matches)
                    logger.info(f"{self.name}: Automatically progressed to Round of 16")
                    return new_matches
        return []

    def get_all_knockout_matches(self) -> List[Match]:
        """Restituisce tutte le partite di knockout (playoff + ottavi + quarti + ecc.)"""
        knockout_matches = []

        if hasattr(self, 'playoff_matches'):
            knockout_matches.extend(self.playoff_matches)

        if hasattr(self, 'round16_matches'):
            knockout_matches.extend(self.round16_matches)

        # Aggiungi altri turni quando li implementeremo
        # if hasattr(self, 'quarter_matches'):
        #     knockout_matches.extend(self.quarter_matches)

        return knockout_matches

    def get_next_unplayed_matches(self, limit: int = 10) -> List[Match]:
        """Restituisce le prossime partite non giocate di tutta la competizione"""
        unplayed = []

        # Prima le partite League Phase
        for match in self.matches:
            if not getattr(match, 'played', False) and not getattr(match, 'is_knockout', False):
                unplayed.append(match)

        # Poi le partite knockout
        for match in self.get_all_knockout_matches():
            if not getattr(match, 'played', False):
                unplayed.append(match)

        # Ordina per data
        unplayed.sort(key=lambda m: getattr(m, 'date', date.today()))

        return unplayed[:limit]

    def simulate_knockout_match(self, match: Match) -> Dict:
        """Simula una singola partita knockout"""
        if getattr(match, 'played', False):
            return {"error": "Match already played"}

        # Ottieni giocatori delle squadre
        home_players = self.get_uefa_team_players(match.home_team)
        away_players = self.get_uefa_team_players(match.away_team)

        # Usa MatchSimulator per partite knockout
        result = match_simulator.simulate_match(match, home_players, away_players)

        # Se è una partita knockout, controlla se serve progressione automatica
        if "error" not in result:
            self.progress_to_next_round()

        return result

    def get_knockout_round_status(self) -> Dict[str, bool]:
        """Restituisce lo stato di completamento dei vari turni"""
        return {
            "league_phase_completed": self.is_league_phase_completed(),
            "playoff_created": hasattr(self, 'playoff_matches'),
            "playoff_completed": self.are_playoff_completed(),
            "round16_created": hasattr(self, 'round16_matches'),
            "round16_completed": hasattr(self, 'round16_matches') and all(getattr(m, 'played', False) for m in self.round16_matches)
        }

def setup_uefa_competitions(season: Season, uefa_teams_data: Optional[Dict] = None) -> Dict[str, UEFACompetitionManager]:
    """Setup competizioni UEFA con dati squadre per simulazione realistica"""
    from ..data.data_loader import data_loader

    competitions = {}

    # Ottieni squadre qualificate
    qualified_teams = data_loader.get_uefa_qualified_teams()

    # Champions League
    cl_teams = qualified_teams.get("Champions League", [])
    if len(cl_teams) >= 32:  # Minimo per avere senso
        champions_league = UEFACompetitionManager(
            "UEFA Champions League",
            season,
            UEFACompetition.CHAMPIONS_LEAGUE,
            36
        )
        champions_league.setup_qualified_teams(cl_teams)
        champions_league.matches = champions_league.generate_league_phase_fixtures()

        # Imposta dati squadre se forniti
        if uefa_teams_data:
            champions_league.set_uefa_teams_data(uefa_teams_data)

        competitions["Champions League"] = champions_league

    # Europa League
    el_teams = qualified_teams.get("Europa League", [])
    if len(el_teams) >= 32:
        europa_league = UEFACompetitionManager(
            "UEFA Europa League",
            season,
            UEFACompetition.EUROPA_LEAGUE,
            36
        )
        europa_league.setup_qualified_teams(el_teams)
        europa_league.matches = europa_league.generate_league_phase_fixtures()

        # Imposta dati squadre se forniti
        if uefa_teams_data:
            europa_league.set_uefa_teams_data(uefa_teams_data)

        competitions["Europa League"] = europa_league

    # Conference League
    ecl_teams = qualified_teams.get("Conference League", [])
    if len(ecl_teams) >= 32:
        conference_league = UEFACompetitionManager(
            "UEFA Conference League",
            season,
            UEFACompetition.CONFERENCE_LEAGUE,
            36
        )
        conference_league.setup_qualified_teams(ecl_teams)
        conference_league.matches = conference_league.generate_league_phase_fixtures()

        # Imposta dati squadre se forniti
        if uefa_teams_data:
            conference_league.set_uefa_teams_data(uefa_teams_data)

        competitions["Conference League"] = conference_league

    logger.info(f"Setup completato: {len(competitions)} competizioni UEFA con simulazione {'realistica' if uefa_teams_data else 'semplificata'}")
    return competitions
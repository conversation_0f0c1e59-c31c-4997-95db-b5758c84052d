"""
Schermata di selezione club e configurazione presidente
"""

import json
import os
from PyQt6.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
                            QPushButton, QGroupBox, QLineEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QScrollArea,
                            QSizePolicy, QSpacerItem)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont

from ui.base_screen import BaseScreen
from core.config import (COLOR_PRIMARY, COLOR_SURFACE, COLOR_BORDER,
                           COLOR_TEXT, BASE_DIR)


class ClubDataLoader(QThread):
    """Thread per caricare i dati delle squadre"""
    data_loaded = pyqtSignal(list)

    def __init__(self, league_name):
        super().__init__()
        self.league_name = league_name

    def run(self):
        """Carica i dati delle squadre dal file JSON"""
        try:
            # Mappa nomi campionati ai file
            league_files = {
                'Serie A': 'Italia/Serie_A.json',
                'Serie B': 'Italia/Serie_B.json',
                'Serie C': ['Italia/Serie C - Girone A.json',
                           'Italia/Serie C - Girone B.json',
                           'Italia/Serie C - Girone C.json']
            }

            clubs = []
            files_to_load = league_files.get(self.league_name, [])

            if isinstance(files_to_load, str):
                files_to_load = [files_to_load]

            for file_path in files_to_load:
                full_path = os.path.join(BASE_DIR, file_path)
                if os.path.exists(full_path):
                    with open(full_path, 'r', encoding='utf-8') as f:
                        league_data = json.load(f)
                        clubs.extend(league_data.get('squadre', []))

            self.data_loaded.emit(clubs)

        except Exception as e:
            print(f"Errore caricamento dati: {e}")
            self.data_loaded.emit([])


class ClubSelectionScreen(BaseScreen):
    """Schermata per selezionare club e configurare presidente"""

    club_selected = pyqtSignal(dict)

    def __init__(self, game_data, parent=None):
        self.game_data = game_data
        self.clubs_data = []
        self.selected_club = None
        super().__init__("Selezione Club", parent)

    def setup_content(self):
        """Configura il contenuto della schermata"""
        # Layout principale
        main_layout = QHBoxLayout()

        # Pannello selezione club (sinistra)
        self.create_club_selection_panel(main_layout)

        # Pannello configurazione presidente (destra)
        self.create_president_panel(main_layout)

        # Pulsanti azioni (in basso)
        bottom_layout = QVBoxLayout()
        bottom_layout.addLayout(main_layout)
        self.create_action_buttons(bottom_layout)

        self.add_content_layout(bottom_layout)

        # Carica i dati delle squadre
        self.load_clubs_data()

    def create_club_selection_panel(self, parent_layout):
        """Crea il pannello di selezione club"""
        club_group = QGroupBox("Scegli la tua squadra")
        club_group.setMinimumWidth(400)
        club_layout = QVBoxLayout(club_group)

        # Filtro campionato
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("Campionato:"))

        self.league_filter = QComboBox()
        self.league_filter.addItems(["Serie A", "Serie B", "Serie C"])
        self.league_filter.setCurrentText(self.game_data.get('main_league', 'Serie A'))
        self.league_filter.currentTextChanged.connect(self.load_clubs_data)
        filter_layout.addWidget(self.league_filter)
        filter_layout.addStretch()

        club_layout.addLayout(filter_layout)

        # Tabella squadre
        self.clubs_table = QTableWidget()
        self.clubs_table.setColumnCount(4)
        self.clubs_table.setHorizontalHeaderLabels([
            "Nome", "Città", "Reputazione", "Budget"
        ])

        # Configura tabella
        header = self.clubs_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)

        self.clubs_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.clubs_table.setAlternatingRowColors(True)
        self.clubs_table.itemSelectionChanged.connect(self.on_club_selected)

        club_layout.addWidget(self.clubs_table)

        # Info squadra selezionata
        self.club_info_label = QLabel("Seleziona una squadra per vedere i dettagli")
        self.club_info_label.setWordWrap(True)
        self.club_info_label.setStyleSheet(f"""
            QLabel {{
                background-color: {COLOR_SURFACE};
                border: 1px solid {COLOR_BORDER};
                border-radius: 4px;
                padding: 10px;
                min-height: 80px;
            }}
        """)
        club_layout.addWidget(self.club_info_label)

        parent_layout.addWidget(club_group)

    def create_president_panel(self, parent_layout):
        """Crea il pannello configurazione presidente"""
        president_group = QGroupBox("Configurazione Presidente")
        president_group.setMinimumWidth(300)
        president_layout = QVBoxLayout(president_group)

        # Nome presidente
        president_layout.addWidget(QLabel("Nome Presidente:"))
        self.president_name = QLineEdit()
        self.president_name.setPlaceholderText("Inserisci il tuo nome")
        president_layout.addWidget(self.president_name)

        # Cognome presidente
        president_layout.addWidget(QLabel("Cognome:"))
        self.president_surname = QLineEdit()
        self.president_surname.setPlaceholderText("Inserisci il tuo cognome")
        president_layout.addWidget(self.president_surname)

        # Nazionalità
        president_layout.addWidget(QLabel("Nazionalità:"))
        self.president_nationality = QComboBox()
        self.president_nationality.addItems([
            "Italiana", "Spagnola", "Francese", "Tedesca", "Inglese",
            "Brasiliana", "Argentina", "Americana", "Altra"
        ])
        president_layout.addWidget(self.president_nationality)

        # Esperienza
        president_layout.addWidget(QLabel("Esperienza manageriale:"))
        self.president_experience = QComboBox()
        self.president_experience.addItems([
            "Principiante", "Dilettante", "Esperto", "Veterano"
        ])
        president_layout.addWidget(self.president_experience)

        president_layout.addStretch()
        parent_layout.addWidget(president_group)

    def create_action_buttons(self, parent_layout):
        """Crea i pulsanti di azione"""
        button_layout = QHBoxLayout()

        # Pulsante Indietro
        back_button = QPushButton("Indietro")
        back_button.setFixedSize(100, 35)
        back_button.clicked.connect(self.back_requested.emit)
        button_layout.addWidget(back_button)

        button_layout.addStretch()

        # Pulsante Continua
        self.continue_button = QPushButton("Inizia Carriera")
        self.continue_button.setFixedSize(150, 35)
        self.continue_button.setEnabled(False)
        self.continue_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #0056b3;
            }}
            QPushButton:disabled {{
                background-color: #ccc;
            }}
        """)
        self.continue_button.clicked.connect(self.start_career)
        button_layout.addWidget(self.continue_button)

        parent_layout.addLayout(button_layout)

    def load_clubs_data(self):
        """Carica i dati delle squadre"""
        league = self.league_filter.currentText()
        self.show_loading("Caricamento squadre...")

        self.loader_thread = ClubDataLoader(league)
        self.loader_thread.data_loaded.connect(self.on_clubs_loaded)
        self.loader_thread.start()

    @pyqtSlot(list)
    def on_clubs_loaded(self, clubs):
        """Gestisce il caricamento completato delle squadre"""
        self.clubs_data = clubs
        self.populate_clubs_table()
        self.hide_loading()

    def populate_clubs_table(self):
        """Popola la tabella con i dati delle squadre"""
        self.clubs_table.setRowCount(len(self.clubs_data))

        for row, club in enumerate(self.clubs_data):
            # Nome
            self.clubs_table.setItem(row, 0, QTableWidgetItem(club['nome']))

            # Città
            self.clubs_table.setItem(row, 1, QTableWidgetItem(club['citta']))

            # Reputazione
            rep_item = QTableWidgetItem(str(club['reputazione']))
            rep_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.clubs_table.setItem(row, 2, rep_item)

            # Budget
            budget = club['budget_trasferimenti_eur']
            budget_text = f"€{budget:,.0f}"
            budget_item = QTableWidgetItem(budget_text)
            budget_item.setTextAlignment(Qt.AlignmentFlag.AlignRight)
            self.clubs_table.setItem(row, 3, budget_item)

    def on_club_selected(self):
        """Gestisce la selezione di una squadra"""
        current_row = self.clubs_table.currentRow()
        if current_row >= 0 and current_row < len(self.clubs_data):
            self.selected_club = self.clubs_data[current_row]
            self.update_club_info()
            self.check_can_continue()

    def update_club_info(self):
        """Aggiorna le informazioni della squadra selezionata"""
        if self.selected_club:
            info_text = f"""
            <b>{self.selected_club['nome']}</b><br>
            Stadio: {self.selected_club['stadio']} ({self.selected_club['capienza_stadio']:,} posti)<br>
            Formazione: {self.selected_club['formazione_predefinita']}<br>
            Budget Trasferimenti: €{self.selected_club['budget_trasferimenti_eur']:,}<br>
            Budget Stipendi: €{self.selected_club['budget_stipendi_eur']:,}/mese
            """
            self.club_info_label.setText(info_text)

    def check_can_continue(self):
        """Verifica se può continuare con la selezione"""
        can_continue = (self.selected_club is not None and
                       self.president_name.text().strip() and
                       self.president_surname.text().strip())

        self.continue_button.setEnabled(can_continue)

    def start_career(self):
        """Avvia la carriera con il club selezionato"""
        if not self.selected_club:
            return

        selection_data = {
            'selected_club': self.selected_club,
            'president': {
                'name': self.president_name.text().strip(),
                'surname': self.president_surname.text().strip(),
                'nationality': self.president_nationality.currentText(),
                'experience': self.president_experience.currentText()
            }
        }

        self.club_selected.emit(selection_data)

    def setup_connections(self):
        """Configura le connessioni dei segnali"""
        self.president_name.textChanged.connect(self.check_can_continue)
        self.president_surname.textChanged.connect(self.check_can_continue)
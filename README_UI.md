# Sistema UI Modale - Football Manager Italiano

## Panoramica

Il sistema UI sviluppato fornisce un'interfaccia grafica moderna e modulare con pannelli riutilizzabili e finestre modali per il Football Manager Italiano. Il sistema è basato su PyQt6 e segue i principi di design modulare e riutilizzabilità.

## Struttura del Sistema

```
src/ui/
├── __init__.py                 # Modulo principale UI
├── main_window.py              # Finestra principale dell'applicazione
├── modal_manager.py            # Gestore modali e pannelli
├── components/                 # Componenti base riutilizzabili
│   ├── __init__.py
│   ├── base_panel.py          # Classe base per pannelli
│   ├── modal_dialog.py        # Dialog modale base
│   ├── button_styles.py       # Stili per bottoni e componenti
│   ├── data_table.py          # Tabella dati riutilizzabile
│   └── form_widgets.py        # Widget per form
└── panels/                     # Pannelli specifici dell'applicazione
    ├── __init__.py
    ├── team_info_panel.py     # Pannello informazioni squadra
    ├── player_list_panel.py   # Pannello lista giocatori
    ├── match_result_panel.py  # Pannello risultato partita
    └── settings_panel.py      # Pannello impostazioni
```

## Caratteristiche Principali

### 1. Architettura Modulare
- **Componenti Riutilizzabili**: Ogni componente è progettato per essere riutilizzato in contesti diversi
- **Separazione delle Responsabilità**: UI, logica e dati sono separati
- **Estensibilità**: Facile aggiungere nuovi pannelli e componenti

### 2. Sistema di Modali Avanzato
- **ModalManager**: Gestisce l'apertura, chiusura e sovrapposizione dei modali
- **Stack Management**: Gestisce l'ordine dei pannelli sovrapposti
- **Animazioni**: Transizioni fluide per apertura/chiusura

### 3. Pannelli Riutilizzabili
- **BasePanel**: Classe base con funzionalità comuni
- **Pannelli Specializzati**: TeamInfo, PlayerList, MatchResult, Settings
- **Comunicazione tramite Segnali**: PyQt signals per comunicazione loose-coupled

### 4. Componenti UI Avanzati
- **DataTable**: Tabella con filtri, ordinamento e paginazione
- **FormWidgets**: Campi form con validazione automatica
- **Stili Consistenti**: Sistema di stili centralizzato

## Utilizzo Base

### 1. Inizializzazione

```python
from src.ui.main_window import MainWindow

# Crea la finestra principale
main_window = MainWindow()
main_window.show()
```

### 2. Apertura di un Pannello

```python
# Apri pannello informazioni squadra
team_data = {
    'name': 'Juventus FC',
    'league': 'Serie A',
    'coach': 'Allegri',
    # ... altri dati
}

panel = main_window.modal_manager.open_panel(
    "team_info",           # ID univoco
    TeamInfoPanel,         # Classe del pannello
    title="Info Squadra"   # Titolo personalizzato
)

panel.set_team_data(team_data)
```

### 3. Apertura di un Dialog

```python
# Apri dialog personalizzato
dialog = main_window.modal_manager.open_dialog(
    "custom_dialog",
    CustomDialog,
    title="Dialog Personalizzato",
    width=500,
    height=400
)

# Gestisci il risultato
dialog.accepted.connect(lambda data: print(f"Accettato: {data}"))
dialog.rejected.connect(lambda: print("Rifiutato"))
```

## Componenti Disponibili

### BasePanel
Classe base per tutti i pannelli con funzionalità comuni:

```python
class CustomPanel(BasePanel):
    def __init__(self, parent=None):
        super().__init__(
            title="Mio Pannello",
            closable=True,
            minimizable=True,
            parent=parent
        )
        self._setup_content()
    
    def _setup_content(self):
        # Aggiungi contenuto al self.content_layout
        pass
    
    def update_content(self):
        # Aggiorna contenuto con self.data
        pass
```

### DataTable
Tabella dati con funzionalità avanzate:

```python
columns = [
    {"key": "name", "title": "Nome", "width": 150, "sortable": True},
    {"key": "age", "title": "Età", "width": 50, "align": "center"},
    {"key": "value", "title": "Valore", "formatter": lambda x: f"€{x:,.0f}"}
]

table = DataTable(columns)
table.set_data(data_list)
table.row_selected.connect(self.on_selection)
```

### FormField
Campi form con validazione:

```python
# Campo testo richiesto
name_field = FormField("Nome", "text", required=True)

# Campo numero con range
age_field = FormField("Età", "number")

# Campo combo con opzioni
position_field = FormField("Ruolo", "combo", options=[
    "Portiere", "Difensore", "Centrocampista", "Attaccante"
])

# Sezione form
section = FormSection("Dati Giocatore", [name_field, age_field, position_field])
```

## Pannelli Inclusi

### 1. TeamInfoPanel
Visualizza informazioni dettagliate di una squadra:
- Header con logo e nome
- Informazioni generali (allenatore, budget, ecc.)
- Statistiche stagionali

### 2. PlayerListPanel
Lista giocatori con filtri e azioni:
- Filtri per ruolo, età, stato
- Tabella ordinabile
- Azioni: visualizza, modifica, trasferisci

### 3. MatchResultPanel
Risultato partita con dettagli:
- Header con punteggio
- Tab: cronaca, statistiche, formazioni
- Azioni per highlights e dettagli

### 4. SettingsPanel
Pannello impostazioni multi-tab:
- Generale, Grafica, Simulazione, Audio
- Form con validazione
- Salvataggio e reset

## Personalizzazione Stili

Il sistema include stili predefiniti configurabili:

```python
from src.ui.components.button_styles import StyleSheet

# Applica stili predefiniti
button.setStyleSheet(StyleSheet.primary_button())
table.setStyleSheet(StyleSheet.data_table())
```

Stili disponibili:
- `primary_button()`, `secondary_button()`, `success_button()`
- `warning_button()`, `danger_button()`
- `data_table()`, `form_input()`, `scrollbar()`

## Configurazione

I colori e font sono configurabili in `src/core/config.py`:

```python
# Colori UI
COLOR_PRIMARY = "#007bff"
COLOR_SECONDARY = "#6c757d"
COLOR_BACKGROUND = "#f8f9fa"
COLOR_SURFACE = "#ffffff"

# Font
MAIN_FONT_FAMILY = "Segoe UI"
MAIN_FONT_SIZE = 9
HEADER_FONT_SIZE = 10
```

## Esempio Completo

Vedere `example_ui_usage.py` per un esempio completo di utilizzo che mostra:
- Inizializzazione del sistema
- Apertura di diversi tipi di pannelli
- Gestione dei segnali
- Passaggio dati tra componenti

## Esecuzione della Demo

```bash
cd footballgame-main
python example_ui_usage.py
```

## Estensione del Sistema

### Aggiungere un Nuovo Pannello

1. Crea una nuova classe che eredita da `BasePanel`
2. Implementa `_setup_content()` e `update_content()`
3. Registra il pannello in `panels/__init__.py`
4. Usa `modal_manager.open_panel()` per aprirlo

### Aggiungere un Nuovo Dialog

1. Crea una classe che eredita da `ModalDialog`
2. Implementa `_setup_demo_content()` e `collect_data()`
3. Usa `modal_manager.open_dialog()` per aprirlo

Il sistema è progettato per essere facilmente estensibile e mantenibile, seguendo le best practices di PyQt6 e i principi di design modulare.
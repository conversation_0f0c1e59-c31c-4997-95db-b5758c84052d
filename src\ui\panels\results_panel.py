"""
Pannello risultati partite e statistiche
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QGroupBox, QTabWidget, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal

from ..components.data_table import DataTable
from ..components.info_display import InfoDisplay
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class ResultsPanel(QWidget):
    """Pannello per visualizzare risultati e statistiche"""

    result_selected = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.results_data = []
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        self.create_panel_header(main_layout)

        # Tab widget principale
        self.tab_widget = QTabWidget()

        # Tab Risultati Recenti
        self.create_recent_results_tab()

        # Tab Statistiche
        self.create_statistics_tab()

        main_layout.addWidget(self.tab_widget)

    def create_panel_header(self, parent_layout):
        """Crea l'header del pannello"""
        header_layout = QHBoxLayout()

        # Titolo
        title_label = QLabel("Risultati e Statistiche")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Filtro competizione
        header_layout.addWidget(QLabel("Competizione:"))
        self.competition_filter = QComboBox()
        self.competition_filter.addItems([
            "Tutte", "Serie A", "Coppa Italia", "Champions League", "Europa League", "Amichevoli"
        ])
        self.competition_filter.currentTextChanged.connect(self.filter_results)
        header_layout.addWidget(self.competition_filter)

        parent_layout.addLayout(header_layout)

    def create_recent_results_tab(self):
        """Crea la tab risultati recenti"""
        results_widget = QWidget()
        layout = QVBoxLayout(results_widget)

        # Tabella risultati
        columns = [
            {"key": "data", "title": "Data", "width": 100},
            {"key": "competizione", "title": "Comp.", "width": 80},
            {"key": "casa", "title": "Casa", "width": 150, "stretch": True},
            {"key": "risultato", "title": "Risultato", "width": 80, "align": "center"},
            {"key": "trasferta", "title": "Trasferta", "width": 150, "stretch": True},
            {"key": "spettatori", "title": "Spettatori", "width": 80, "align": "right",
             "formatter": lambda x: f"{x:,}" if isinstance(x, int) else "N/A"}
        ]

        self.results_table = DataTable(columns)
        self.results_table.row_selected.connect(self.on_result_selected)
        layout.addWidget(self.results_table)

        # Carica dati di esempio
        self.load_sample_results()

        self.tab_widget.addTab(results_widget, "📊 Risultati")

    def create_statistics_tab(self):
        """Crea la tab statistiche"""
        stats_widget = QWidget()
        layout = QVBoxLayout(stats_widget)

        # Layout a due colonne
        stats_layout = QHBoxLayout()

        # Colonna sinistra - Statistiche generali
        general_group = QGroupBox("Statistiche Generali")
        general_layout = QVBoxLayout(general_group)

        self.general_stats = InfoDisplay()
        general_layout.addWidget(self.general_stats)

        stats_layout.addWidget(general_group)

        # Colonna destra - Record e primati
        records_group = QGroupBox("Record Stagionali")
        records_layout = QVBoxLayout(records_group)

        self.records_stats = InfoDisplay()
        records_layout.addWidget(self.records_stats)

        stats_layout.addWidget(records_group)

        layout.addLayout(stats_layout)

        # Aggiorna statistiche
        self.update_statistics()

        self.tab_widget.addTab(stats_widget, "📈 Statistiche")

    def load_sample_results(self):
        """Carica risultati di esempio"""
        sample_results = [
            {
                "data": "2025-08-20", "competizione": "Serie A",
                "casa": "Juventus", "risultato": "2-1", "trasferta": "Como",
                "spettatori": 41000
            },
            {
                "data": "2025-08-27", "competizione": "Serie A",
                "casa": "Napoli", "risultato": "1-1", "trasferta": "Juventus",
                "spettatori": 55000
            },
            {
                "data": "2025-09-03", "competizione": "Serie A",
                "casa": "Juventus", "risultato": "3-0", "trasferta": "Verona",
                "spettatori": 40500
            }
        ]
        
        self.results_data = sample_results
        self.results_table.set_data(sample_results)

    def filter_results(self):
        """Filtra i risultati per competizione"""
        competition = self.competition_filter.currentText()
        
        if competition == "Tutte":
            filtered_data = self.results_data
        else:
            filtered_data = [r for r in self.results_data if r['competizione'] == competition]
        
        self.results_table.set_data(filtered_data)

    def on_result_selected(self, row_index, result_data):
        """Gestisce la selezione di un risultato"""
        self.result_selected.emit(result_data)

    def update_statistics(self):
        """Aggiorna le statistiche"""
        # Statistiche generali
        general_data = {
            'Partite Giocate': 3,
            'Vittorie': 2,
            'Pareggi': 1,
            'Sconfitte': 0,
            'Gol Fatti': 6,
            'Gol Subiti': 2,
            'Differenza Reti': '+4',
            'Punti': 7
        }
        self.general_stats.set_data(general_data)

        # Record stagionali
        records_data = {
            'Vittoria più Larga': '3-0 vs Verona',
            'Miglior Prestazione': 'vs Como (2-1)',
            'Imbattibilità': '3 partite',
            'Goleador': 'Vlahovic (3 gol)',
            'Assist Man': 'Chiesa (2 assist)',
            'Clean Sheet': '1 partita'
        }
        self.records_stats.set_data(records_data)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'results',
            'results_count': len(self.results_data),
            'competition_filter': self.competition_filter.currentText()
        }

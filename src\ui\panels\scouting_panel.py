"""
Pannello scouting e osservatori
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QH<PERSON><PERSON>Layout, QLabel,
                            QGroupBox, QTabWidget, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal

from ..components.data_table import DataTable
from ..components.info_display import InfoDisplay
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class ScoutingPanel(QWidget):
    """Pannello per scouting e osservatori"""

    scout_assigned = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        header_layout = QHBoxLayout()
        title_label = QLabel("Scouting e Osservatori")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        main_layout.addLayout(header_layout)

        # Tab widget
        self.tab_widget = QTabWidget()

        # Tab Osservatori
        scouts_widget = QWidget()
        scouts_layout = QVBoxLayout(scouts_widget)
        
        self.scouts_table = DataTable([
            {"key": "nome", "title": "Nome", "width": 150, "stretch": True},
            {"key": "specializzazione", "title": "Specializzazione", "width": 120},
            {"key": "regione", "title": "Regione", "width": 100},
            {"key": "stato", "title": "Stato", "width": 80, "align": "center"}
        ])
        scouts_layout.addWidget(self.scouts_table)
        
        self.tab_widget.addTab(scouts_widget, "🔍 Osservatori")

        # Tab Report
        reports_widget = QWidget()
        reports_layout = QVBoxLayout(reports_widget)
        
        self.reports_table = DataTable([
            {"key": "giocatore", "title": "Giocatore", "width": 150, "stretch": True},
            {"key": "squadra", "title": "Squadra", "width": 120},
            {"key": "valutazione", "title": "Valutazione", "width": 100, "align": "center"},
            {"key": "data", "title": "Data", "width": 100, "align": "center"}
        ])
        reports_layout.addWidget(self.reports_table)
        
        self.tab_widget.addTab(reports_widget, "📊 Report")

        main_layout.addWidget(self.tab_widget)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {'type': 'scouting'}

"""
Sistema di validazioni condiviso per eliminare duplicazioni
Fornisce validatori riusabili per diversi tipi di dati
"""
from typing import Any, List, Dict, Optional, Union, Callable
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
import re
import logging

logger = logging.getLogger(__name__)


@dataclass
class ValidationError:
    """Rappresenta un errore di validazione"""
    field: str
    message: str
    value: Any = None
    code: str = "INVALID"


@dataclass
class ValidationResult:
    """Risultato di una validazione"""
    is_valid: bool
    errors: List[ValidationError]

    def add_error(self, field: str, message: str, value: Any = None, code: str = "INVALID"):
        """Aggiunge un errore di validazione"""
        self.errors.append(ValidationError(field, message, value, code))
        self.is_valid = False

    def get_errors_for_field(self, field: str) -> List[ValidationError]:
        """Restituisce gli errori per un campo specifico"""
        return [error for error in self.errors if error.field == field]

    def get_error_messages(self) -> List[str]:
        """Restituisce tutti i messaggi di errore"""
        return [error.message for error in self.errors]


class BaseValidator(ABC):
    """Classe base per tutti i validatori"""

    def __init__(self, required: bool = True, allow_empty: bool = False):
        self.required = required
        self.allow_empty = allow_empty

    @abstractmethod
    def validate(self, value: Any, field_name: str = "field") -> ValidationResult:
        """Valida un valore"""
        pass

    def _check_required(self, value: Any, field_name: str) -> Optional[ValidationResult]:
        """Controlla se il campo richiesto è presente"""
        if self.required and (value is None or (not self.allow_empty and str(value).strip() == "")):
            result = ValidationResult(False, [])
            result.add_error(field_name, f"Il campo {field_name} è obbligatorio", value, "REQUIRED")
            return result
        return None


class StringValidator(BaseValidator):
    """Validatore per stringhe"""

    def __init__(self, min_length: int = 0, max_length: int = None,
                 pattern: str = None, **kwargs):
        super().__init__(**kwargs)
        self.min_length = min_length
        self.max_length = max_length
        self.pattern = re.compile(pattern) if pattern else None

    def validate(self, value: Any, field_name: str = "field") -> ValidationResult:
        result = ValidationResult(True, [])

        # Controllo required
        required_check = self._check_required(value, field_name)
        if required_check:
            return required_check

        if value is None:
            return result

        # Converti a stringa
        str_value = str(value)

        # Controllo lunghezza minima
        if len(str_value) < self.min_length:
            result.add_error(field_name,
                           f"Il campo {field_name} deve essere lungo almeno {self.min_length} caratteri",
                           value, "MIN_LENGTH")

        # Controllo lunghezza massima
        if self.max_length and len(str_value) > self.max_length:
            result.add_error(field_name,
                           f"Il campo {field_name} deve essere lungo al massimo {self.max_length} caratteri",
                           value, "MAX_LENGTH")

        # Controllo pattern
        if self.pattern and not self.pattern.match(str_value):
            result.add_error(field_name,
                           f"Il campo {field_name} non rispetta il formato richiesto",
                           value, "PATTERN")

        return result


class NumberValidator(BaseValidator):
    """Validatore per numeri"""

    def __init__(self, min_value: Union[int, float] = None,
                 max_value: Union[int, float] = None,
                 integer_only: bool = False, **kwargs):
        super().__init__(**kwargs)
        self.min_value = min_value
        self.max_value = max_value
        self.integer_only = integer_only

    def validate(self, value: Any, field_name: str = "field") -> ValidationResult:
        result = ValidationResult(True, [])

        # Controllo required
        required_check = self._check_required(value, field_name)
        if required_check:
            return required_check

        if value is None:
            return result

        # Converti a numero
        try:
            if self.integer_only:
                num_value = int(value)
            else:
                num_value = float(value)
        except (ValueError, TypeError):
            result.add_error(field_name,
                           f"Il campo {field_name} deve essere un numero valido",
                           value, "INVALID_NUMBER")
            return result

        # Controllo valore minimo
        if self.min_value is not None and num_value < self.min_value:
            result.add_error(field_name,
                           f"Il campo {field_name} deve essere maggiore o uguale a {self.min_value}",
                           value, "MIN_VALUE")

        # Controllo valore massimo
        if self.max_value is not None and num_value > self.max_value:
            result.add_error(field_name,
                           f"Il campo {field_name} deve essere minore o uguale a {self.max_value}",
                           value, "MAX_VALUE")

        return result


class EmailValidator(StringValidator):
    """Validatore per email"""

    def __init__(self, **kwargs):
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        super().__init__(pattern=email_pattern, **kwargs)

    def validate(self, value: Any, field_name: str = "field") -> ValidationResult:
        result = super().validate(value, field_name)

        # Sostituisci il messaggio di errore pattern con uno più specifico
        for error in result.errors:
            if error.code == "PATTERN":
                error.message = f"Il campo {field_name} deve contenere un indirizzo email valido"

        return result


class FileValidator(BaseValidator):
    """Validatore per file"""

    def __init__(self, allowed_extensions: List[str] = None,
                 max_size_mb: float = None, must_exist: bool = True, **kwargs):
        super().__init__(**kwargs)
        self.allowed_extensions = [ext.lower() for ext in (allowed_extensions or [])]
        self.max_size_mb = max_size_mb
        self.must_exist = must_exist

    def validate(self, value: Any, field_name: str = "field") -> ValidationResult:
        result = ValidationResult(True, [])

        # Controllo required
        required_check = self._check_required(value, field_name)
        if required_check:
            return required_check

        if value is None:
            return result

        # Converti a Path
        try:
            file_path = Path(str(value))
        except Exception:
            result.add_error(field_name,
                           f"Il campo {field_name} deve essere un percorso file valido",
                           value, "INVALID_PATH")
            return result

        # Controllo esistenza
        if self.must_exist and not file_path.exists():
            result.add_error(field_name,
                           f"Il file {field_name} non esiste",
                           value, "FILE_NOT_FOUND")
            return result

        # Se il file non esiste ma non è richiesto, non fare altri controlli
        if not file_path.exists():
            return result

        # Controllo estensione
        if self.allowed_extensions and file_path.suffix.lower() not in self.allowed_extensions:
            result.add_error(field_name,
                           f"Il file {field_name} deve avere una delle seguenti estensioni: {', '.join(self.allowed_extensions)}",
                           value, "INVALID_EXTENSION")

        # Controllo dimensione
        if self.max_size_mb:
            try:
                size_mb = file_path.stat().st_size / (1024 * 1024)
                if size_mb > self.max_size_mb:
                    result.add_error(field_name,
                                   f"Il file {field_name} non può essere più grande di {self.max_size_mb}MB",
                                   value, "FILE_TOO_LARGE")
            except Exception as e:
                logger.warning(f"Impossibile controllare la dimensione del file {file_path}: {e}")

        return result


class ChoiceValidator(BaseValidator):
    """Validatore per scelte da una lista"""

    def __init__(self, choices: List[Any], case_sensitive: bool = True, **kwargs):
        super().__init__(**kwargs)
        self.choices = choices if case_sensitive else [str(c).lower() for c in choices]
        self.case_sensitive = case_sensitive

    def validate(self, value: Any, field_name: str = "field") -> ValidationResult:
        result = ValidationResult(True, [])

        # Controllo required
        required_check = self._check_required(value, field_name)
        if required_check:
            return required_check

        if value is None:
            return result

        # Controllo scelta
        check_value = value if self.case_sensitive else str(value).lower()
        if check_value not in self.choices:
            display_choices = self.choices if self.case_sensitive else [str(c) for c in self.choices]
            result.add_error(field_name,
                           f"Il campo {field_name} deve essere uno tra: {', '.join(map(str, display_choices))}",
                           value, "INVALID_CHOICE")

        return result


class FormValidator:
    """Validatore per form completi"""

    def __init__(self):
        self.validators: Dict[str, BaseValidator] = {}
        self.custom_validators: Dict[str, Callable] = {}

    def add_validator(self, field_name: str, validator: BaseValidator):
        """Aggiunge un validatore per un campo"""
        self.validators[field_name] = validator
        return self

    def add_custom_validator(self, field_name: str, validator_func: Callable):
        """Aggiunge un validatore custom per un campo"""
        self.custom_validators[field_name] = validator_func
        return self

    def validate_form(self, data: Dict[str, Any]) -> ValidationResult:
        """Valida un form completo"""
        result = ValidationResult(True, [])

        # Valida ogni campo con il suo validatore
        for field_name, validator in self.validators.items():
            field_value = data.get(field_name)
            field_result = validator.validate(field_value, field_name)

            if not field_result.is_valid:
                result.is_valid = False
                result.errors.extend(field_result.errors)

        # Valida con validatori custom
        for field_name, validator_func in self.custom_validators.items():
            try:
                field_value = data.get(field_name)
                custom_result = validator_func(field_value, field_name, data)

                if isinstance(custom_result, ValidationResult):
                    if not custom_result.is_valid:
                        result.is_valid = False
                        result.errors.extend(custom_result.errors)
                elif not custom_result:  # Se restituisce False
                    result.add_error(field_name, f"Validazione fallita per il campo {field_name}")

            except Exception as e:
                logger.error(f"Errore nel validatore custom per {field_name}: {e}")
                result.add_error(field_name, f"Errore di validazione per il campo {field_name}")

        return result


class DataValidator:
    """Validatore per strutture dati complesse"""

    @staticmethod
    def validate_player_data(player_data: Dict[str, Any]) -> ValidationResult:
        """Valida i dati di un giocatore"""
        validator = FormValidator()

        validator.add_validator("name", StringValidator(min_length=2, max_length=50))
        validator.add_validator("age", NumberValidator(min_value=16, max_value=50, integer_only=True))
        validator.add_validator("position", ChoiceValidator(
            ["GK", "DEF", "MID", "ATT"], case_sensitive=False
        ))
        validator.add_validator("overall", NumberValidator(min_value=1, max_value=100, integer_only=True))
        validator.add_validator("potential", NumberValidator(min_value=1, max_value=100, integer_only=True))

        # Validatore custom: il potenziale deve essere >= overall
        def validate_potential(value, field_name, all_data):
            overall = all_data.get("overall", 0)
            if value and overall and value < overall:
                result = ValidationResult(False, [])
                result.add_error(field_name, "Il potenziale deve essere maggiore o uguale all'overall")
                return result
            return ValidationResult(True, [])

        validator.add_custom_validator("potential", validate_potential)

        return validator.validate_form(player_data)

    @staticmethod
    def validate_team_data(team_data: Dict[str, Any]) -> ValidationResult:
        """Valida i dati di una squadra"""
        validator = FormValidator()

        validator.add_validator("name", StringValidator(min_length=2, max_length=100))
        validator.add_validator("league", StringValidator(min_length=2, max_length=50))
        validator.add_validator("reputation", NumberValidator(min_value=1, max_value=10, integer_only=True))
        validator.add_validator("budget", NumberValidator(min_value=0))

        return validator.validate_form(team_data)

    @staticmethod
    def validate_match_data(match_data: Dict[str, Any]) -> ValidationResult:
        """Valida i dati di una partita"""
        validator = FormValidator()

        validator.add_validator("home_team", StringValidator(min_length=2, max_length=100))
        validator.add_validator("away_team", StringValidator(min_length=2, max_length=100))
        validator.add_validator("home_score", NumberValidator(min_value=0, integer_only=True))
        validator.add_validator("away_score", NumberValidator(min_value=0, integer_only=True))
        validator.add_validator("date", StringValidator(min_length=8))

        # Validatore custom: le squadre devono essere diverse
        def validate_different_teams(value, field_name, all_data):
            if field_name == "away_team":
                home_team = all_data.get("home_team")
                if value and home_team and value == home_team:
                    result = ValidationResult(False, [])
                    result.add_error(field_name, "La squadra in trasferta deve essere diversa da quella di casa")
                    return result
            return ValidationResult(True, [])

        validator.add_custom_validator("away_team", validate_different_teams)

        return validator.validate_form(match_data)


# Factory per validatori comuni
class ValidatorFactory:
    """Factory per creare validatori comuni"""

    @staticmethod
    def create_name_validator(min_length: int = 2, max_length: int = 50) -> StringValidator:
        """Crea un validatore per nomi"""
        return StringValidator(
            min_length=min_length,
            max_length=max_length,
            pattern=r'^[a-zA-ZÀ-ÿ\s\-\'\.]+$'  # Lettere, spazi, trattini, apostrofi, punti
        )

    @staticmethod
    def create_age_validator(min_age: int = 16, max_age: int = 50) -> NumberValidator:
        """Crea un validatore per età"""
        return NumberValidator(min_value=min_age, max_value=max_age, integer_only=True)

    @staticmethod
    def create_rating_validator(min_rating: int = 1, max_rating: int = 100) -> NumberValidator:
        """Crea un validatore per rating/valutazioni"""
        return NumberValidator(min_value=min_rating, max_value=max_rating, integer_only=True)

    @staticmethod
    def create_position_validator() -> ChoiceValidator:
        """Crea un validatore per posizioni di gioco"""
        positions = ["GK", "CB", "LB", "RB", "CM", "LM", "RM", "CAM", "LW", "RW", "ST"]
        return ChoiceValidator(positions, case_sensitive=False)

    @staticmethod
    def create_league_validator() -> ChoiceValidator:
        """Crea un validatore per leghe"""
        leagues = ["Serie A", "Premier League", "La Liga", "Bundesliga", "Ligue 1"]
        return ChoiceValidator(leagues, case_sensitive=False)


# Decoratori per validazione
def validate_input(**validators):
    """Decoratore per validare automaticamente gli input di una funzione"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Valida kwargs
            for param_name, validator in validators.items():
                if param_name in kwargs:
                    result = validator.validate(kwargs[param_name], param_name)
                    if not result.is_valid:
                        raise ValueError(f"Validazione fallita per {param_name}: {result.get_error_messages()}")

            return func(*args, **kwargs)
        return wrapper
    return decorator


# Esempio di utilizzo avanzato
if __name__ == "__main__":
    # Test validazione giocatore
    player_data = {
        "name": "Mario Rossi",
        "age": 25,
        "position": "MID",
        "overall": 75,
        "potential": 80
    }

    result = DataValidator.validate_player_data(player_data)
    print(f"Giocatore valido: {result.is_valid}")
    if not result.is_valid:
        for error in result.errors:
            print(f"- {error.field}: {error.message}")
#!/usr/bin/env python3
"""
Script per analizzare le squadre generate
"""
import sys
from typing import List, Dict, Optional
from src.players.squad_persistence import squad_persistence
from src.players.player import Player

def show_available_files():
    """Mostra file squadre disponibili"""
    print("📁 File squadre disponibili:\n")
    
    saved_files = squad_persistence.list_saved_squads()
    
    if not saved_files:
        print("   Nessun file trovato.")
        return None
    
    for i, file_info in enumerate(saved_files, 1):
        print(f"   {i}. {file_info['filename']}")
        print(f"      📅 {file_info['generated_at']}")
        print(f"      🏟️  {file_info['total_teams']} squadre, 👥 {file_info['total_players']} giocatori")
        print(f"      📦 {file_info['file_size_mb']:.1f} MB\n")
    
    return saved_files

def analyze_team(squad: List[Player], team_name: str, country: str) -> Dict:
    """Analizza una singola squadra"""
    
    if not squad:
        return {}
    
    analysis = {
        "name": team_name,
        "country": country,
        "total_players": len(squad),
        "avg_overall": sum(p.overall_rating for p in squad) / len(squad),
        "max_overall": max(p.overall_rating for p in squad),
        "min_overall": min(p.overall_rating for p in squad),
        "avg_age": sum(p.age for p in squad) / len(squad),
        "oldest_player": max(p.age for p in squad),
        "youngest_player": min(p.age for p in squad),
        "avg_market_value": sum(p.market_value for p in squad) / len(squad),
        "total_market_value": sum(p.market_value for p in squad),
        "star_players": len([p for p in squad if p.overall_rating >= 17]),
        "youth_players": len([p for p in squad if p.age <= 21]),
        "veterans": len([p for p in squad if p.age >= 32]),
        "nationalities": len(set(p.nationality for p in squad)),
        "most_common_nationality": None,
        "positions": {}
    }
    
    # Nazionalità più comune
    nationality_count = {}
    for player in squad:
        nationality_count[player.nationality] = nationality_count.get(player.nationality, 0) + 1
    
    if nationality_count:
        analysis["most_common_nationality"] = max(nationality_count.items(), key=lambda x: x[1])
    
    # Posizioni
    for player in squad:
        pos = player.position.value
        analysis["positions"][pos] = analysis["positions"].get(pos, 0) + 1
    
    return analysis

def find_best_teams(squads_data: Dict, top_n: int = 10, criteria: str = "overall") -> List[Dict]:
    """Trova le migliori squadre secondo diversi criteri"""
    
    all_analyses = []
    
    for country, leagues in squads_data.items():
        for league_name, teams in leagues.items():
            for team_name, squad in teams.items():
                analysis = analyze_team(squad, team_name, country)
                if analysis:
                    analysis["league"] = league_name
                    all_analyses.append(analysis)
    
    # Ordina per criterio
    if criteria == "overall":
        all_analyses.sort(key=lambda x: x["avg_overall"], reverse=True)
    elif criteria == "value":
        all_analyses.sort(key=lambda x: x["total_market_value"], reverse=True)
    elif criteria == "stars":
        all_analyses.sort(key=lambda x: x["star_players"], reverse=True)
    elif criteria == "youth":
        all_analyses.sort(key=lambda x: x["youth_players"], reverse=True)
    
    return all_analyses[:top_n]

def compare_leagues(squads_data: Dict) -> Dict[str, Dict]:
    """Confronta i campionati europei"""
    
    league_stats = {}
    
    for country, leagues in squads_data.items():
        for league_name, teams in leagues.items():
            full_league_name = f"{league_name} ({country})"
            
            all_players = []
            team_analyses = []
            
            for team_name, squad in teams.items():
                all_players.extend(squad)
                analysis = analyze_team(squad, team_name, country)
                if analysis:
                    team_analyses.append(analysis)
            
            if not all_players:
                continue
            
            league_stats[full_league_name] = {
                "country": country,
                "teams": len(teams),
                "total_players": len(all_players),
                "avg_overall": sum(p.overall_rating for p in all_players) / len(all_players),
                "avg_age": sum(p.age for p in all_players) / len(all_players),
                "total_value": sum(p.market_value for p in all_players),
                "star_players": len([p for p in all_players if p.overall_rating >= 17]),
                "youth_players": len([p for p in all_players if p.age <= 21]),
                "best_team": max(team_analyses, key=lambda x: x["avg_overall"])["name"] if team_analyses else "N/A",
                "worst_team": min(team_analyses, key=lambda x: x["avg_overall"])["name"] if team_analyses else "N/A"
            }
    
    return league_stats

def interactive_analysis():
    """Analisi interattiva delle squadre"""
    
    print("🔍 ANALIZZATORE SQUADRE EUROPEE")
    print("=" * 40)
    
    # Mostra file disponibili
    saved_files = show_available_files()
    if not saved_files:
        return
    
    # Selezione file
    while True:
        try:
            choice = input("Seleziona file (numero): ").strip()
            file_index = int(choice) - 1
            if 0 <= file_index < len(saved_files):
                selected_file = saved_files[file_index]["filename"]
                break
            else:
                print("❌ Numero non valido!")
        except ValueError:
            print("❌ Inserisci un numero valido!")
    
    print(f"\n📂 Caricamento {selected_file}...")
    
    # Carica dati
    squads_data = squad_persistence.load_squads(selected_file)
    if not squads_data:
        print("❌ Errore caricamento file!")
        return
    
    while True:
        print(f"\n🔍 OPZIONI ANALISI:")
        print("1. 🏆 Top 10 squadre per overall")
        print("2. 💰 Top 10 squadre per valore")  
        print("3. ⭐ Top 10 squadre per stelle (Overall 17+)")
        print("4. 👶 Top 10 squadre per giovani (≤21 anni)")
        print("5. 🏟️  Confronto campionati")
        print("6. 🔍 Analisi squadra specifica")
        print("7. 📊 Statistiche generali")
        print("0. ❌ Esci")
        
        choice = input("\nScelta: ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            show_top_teams(squads_data, "overall")
        elif choice == "2":
            show_top_teams(squads_data, "value")
        elif choice == "3":
            show_top_teams(squads_data, "stars")
        elif choice == "4":
            show_top_teams(squads_data, "youth")
        elif choice == "5":
            show_league_comparison(squads_data)
        elif choice == "6":
            analyze_specific_team(squads_data)
        elif choice == "7":
            show_general_stats(squads_data)
        else:
            print("❌ Opzione non valida!")

def show_top_teams(squads_data: Dict, criteria: str):
    """Mostra top squadre per criterio"""
    
    criteria_names = {
        "overall": "Overall Medio",
        "value": "Valore Totale",
        "stars": "Giocatori Stella",
        "youth": "Giovani Promesse"
    }
    
    print(f"\n🏆 TOP 10 SQUADRE PER {criteria_names[criteria].upper()}")
    print("-" * 60)
    
    top_teams = find_best_teams(squads_data, 10, criteria)
    
    if criteria == "overall":
        print(f"{'#':<2} {'Squadra':<25} {'Paese':<15} {'Overall':<8} {'⭐':<3} {'👶':<3}")
    elif criteria == "value":
        print(f"{'#':<2} {'Squadra':<25} {'Paese':<15} {'Valore €M':<10} {'Overall':<8}")
    elif criteria == "stars":
        print(f"{'#':<2} {'Squadra':<25} {'Paese':<15} {'Stelle':<6} {'Overall':<8}")
    elif criteria == "youth":
        print(f"{'#':<2} {'Squadra':<25} {'Paese':<15} {'Giovani':<7} {'Overall':<8}")
    
    print("-" * 60)
    
    for i, team in enumerate(top_teams, 1):
        name = team["name"][:24]  # Tronca se troppo lungo
        
        if criteria == "overall":
            print(f"{i:<2} {name:<25} {team['country']:<15} {team['avg_overall']:<8.1f} "
                  f"{team['star_players']:<3} {team['youth_players']:<3}")
        elif criteria == "value":
            value_millions = team['total_market_value'] / 1_000_000
            print(f"{i:<2} {name:<25} {team['country']:<15} {value_millions:<10.1f} {team['avg_overall']:<8.1f}")
        elif criteria == "stars":
            print(f"{i:<2} {name:<25} {team['country']:<15} {team['star_players']:<6} {team['avg_overall']:<8.1f}")
        elif criteria == "youth":
            print(f"{i:<2} {name:<25} {team['country']:<15} {team['youth_players']:<7} {team['avg_overall']:<8.1f}")

def show_league_comparison(squads_data: Dict):
    """Confronta i campionati"""
    
    print(f"\n🏟️  CONFRONTO CAMPIONATI EUROPEI")
    print("-" * 80)
    
    league_stats = compare_leagues(squads_data)
    
    # Ordina per overall medio
    sorted_leagues = sorted(league_stats.items(), key=lambda x: x[1]["avg_overall"], reverse=True)
    
    print(f"{'Campionato':<30} {'Squadre':<7} {'Overall':<8} {'⭐':<4} {'👶':<4} {'€M Tot':<8}")
    print("-" * 80)
    
    for league_name, stats in sorted_leagues:
        value_millions = stats['total_value'] / 1_000_000
        print(f"{league_name[:29]:<30} {stats['teams']:<7} {stats['avg_overall']:<8.1f} "
              f"{stats['star_players']:<4} {stats['youth_players']:<4} {value_millions:<8.0f}")

def analyze_specific_team(squads_data: Dict):
    """Analizza una squadra specifica"""
    
    print(f"\n🔍 ANALISI SQUADRA SPECIFICA")
    
    # Lista tutte le squadre
    all_teams = []
    for country, leagues in squads_data.items():
        for league_name, teams in leagues.items():
            for team_name in teams.keys():
                all_teams.append((team_name, country, league_name))
    
    all_teams.sort()
    
    print(f"\nSquadre disponibili ({len(all_teams)}):")
    for i, (team, country, league) in enumerate(all_teams[:20], 1):  # Mostra prime 20
        print(f"   {i}. {team} ({country})")
    
    if len(all_teams) > 20:
        print(f"   ... e altre {len(all_teams) - 20} squadre")
    
    team_name = input(f"\nNome squadra (esatto): ").strip()
    
    # Cerca squadra
    found = False
    for country, leagues in squads_data.items():
        for league_name, teams in leagues.items():
            if team_name in teams:
                squad = teams[team_name]
                analysis = analyze_team(squad, team_name, country)
                
                print(f"\n⚽ {team_name} ({country} - {league_name})")
                print("-" * 50)
                print(f"👥 Giocatori: {analysis['total_players']}")
                print(f"📊 Overall: {analysis['avg_overall']:.1f} (min: {analysis['min_overall']}, max: {analysis['max_overall']})")
                print(f"🎂 Età media: {analysis['avg_age']:.1f} (min: {analysis['youngest_player']}, max: {analysis['oldest_player']})")
                print(f"💰 Valore totale: €{analysis['total_market_value']:,}")
                print(f"⭐ Stelle (17+): {analysis['star_players']}")
                print(f"👶 Giovani (≤21): {analysis['youth_players']}")
                print(f"👴 Veterani (≥32): {analysis['veterans']}")
                print(f"🌍 Nazionalità diverse: {analysis['nationalities']}")
                
                if analysis['most_common_nationality']:
                    nation, count = analysis['most_common_nationality']
                    print(f"🏳️  Nazionalità principale: {nation} ({count} giocatori)")
                
                print(f"\n📍 Distribuzione posizioni:")
                for pos, count in sorted(analysis['positions'].items()):
                    print(f"   {pos}: {count}")
                
                found = True
                break
        if found:
            break
    
    if not found:
        print(f"❌ Squadra '{team_name}' non trovata!")

def show_general_stats(squads_data: Dict):
    """Mostra statistiche generali"""
    
    summary = squad_persistence.get_squad_summary(squads_data)
    
    print(f"\n📊 STATISTICHE GENERALI")
    print("-" * 40)
    print(f"🌍 Paesi: {summary['countries']}")
    print(f"🏟️  Campionati: {summary['leagues']}")
    print(f"⚽ Squadre: {summary['teams']}")
    print(f"👥 Giocatori totali: {summary['total_players']}")
    
    print(f"\n🔝 Top 10 nazionalità:")
    top_nations = sorted(summary['nationality_distribution'].items(), 
                        key=lambda x: x[1], reverse=True)[:10]
    for nation, count in top_nations:
        percentage = count / summary['total_players'] * 100
        print(f"   {nation}: {count} ({percentage:.1f}%)")
    
    print(f"\n📈 Distribuzione Overall:")
    for overall_range, count in summary['overall_distribution'].items():
        if count > 0:
            percentage = count / summary['total_players'] * 100
            print(f"   {overall_range}: {count} ({percentage:.1f}%)")

def main():
    """Main function"""
    
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("Analizzatore Squadre Europee")
        print()
        print("Uso:")
        print("  python analyze_squads.py    # Avvia analisi interattiva")
        return
    
    interactive_analysis()

if __name__ == "__main__":
    main()
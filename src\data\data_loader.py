"""
Data Loader per caricamento dati dai file JSON
"""
import json
import codecs
from typing import Dict, List, Optional, Any
from pathlib import Path

from ..core.config import *
from ..core.utils import logger, load_json_file

class DataLoader:
    """Classe per il caricamento e gestione dati JSON"""

    def __init__(self):
        self.leagues_data = {}
        self.player_names_data = {}
        self.fifa_codes_data = {}
        self._loaded = False

    def load_all_data(self) -> bool:
        """Carica tutti i dati necessari"""
        try:
            logger.info("Inizio caricamento dati...")

            # Carica nomi giocatori
            if not self._load_player_names():
                logger.error("Errore caricamento nomi giocatori")
                return False

            # Carica codici FIFA
            if not self._load_fifa_codes():
                logger.error("Errore caricamento codici FIFA")
                return False

            # Carica campionati
            if not self._load_all_leagues():
                logger.error("Errore caricamento campionati")
                return False

            self._loaded = True
            logger.info("Caricamento dati completato con successo")
            return True

        except Exception as e:
            logger.error(f"Errore durante caricamento dati: {e}")
            return False

    def _load_player_names(self) -> bool:
        """Carica i nomi dei giocatori dalle 110 nazioni"""
        try:
            self.player_names_data = load_json_file(PLAYER_NAMES_FILE)
            if not self.player_names_data:
                logger.error(f"File nomi giocatori non trovato o vuoto: {PLAYER_NAMES_FILE}")
                return False

            logger.info(f"Caricati nomi per {len(self.player_names_data)} nazioni")
            return True

        except Exception as e:
            logger.error(f"Errore caricamento nomi giocatori: {e}")
            return False

    def _load_fifa_codes(self) -> bool:
        """Carica i codici FIFA dei paesi"""
        try:
            if FIFA_CODES_FILE.exists():
                self.fifa_codes_data = load_json_file(FIFA_CODES_FILE)
                logger.info(f"Caricati codici FIFA per {len(self.fifa_codes_data)} paesi")
            return True

        except Exception as e:
            logger.error(f"Errore caricamento codici FIFA: {e}")
            return False

    def _load_all_leagues(self) -> bool:
        """Carica tutti i campionati disponibili"""
        try:
            # Carica campionati italiani
            italy_leagues = self._load_country_leagues("Italia")
            if italy_leagues:
                self.leagues_data["Italia"] = italy_leagues
                logger.info(f"Caricati {len(italy_leagues)} campionati italiani")

            # Carica tutti i campionati europei per competizioni UEFA
            from ..competitions.coefficient_system import uefa_coefficient_system
            uefa_countries = list(uefa_coefficient_system.coefficients.keys())

            for country in uefa_countries:
                if country != "Italia":  # Italia già caricata
                    country_leagues = self._load_country_leagues(country)
                    if country_leagues:
                        self.leagues_data[country] = country_leagues

            return len(self.leagues_data) > 0

        except Exception as e:
            logger.error(f"Errore caricamento campionati: {e}")
            return False

    def _load_country_leagues(self, country: str) -> Dict:
        """Carica i campionati di una nazione"""
        country_dir = BASE_DIR / country
        leagues = {}

        if not country_dir.exists():
            logger.warning(f"Directory non trovata: {country_dir}")
            return leagues

        try:
            # Cerca tutti i file JSON nella directory del paese
            json_files = list(country_dir.glob("*.json"))

            for json_file in json_files:
                league_data = load_json_file(json_file)
                if league_data:
                    league_name = json_file.stem.replace("_", " ")
                    leagues[league_name] = league_data

            return leagues

        except Exception as e:
            logger.error(f"Errore caricamento campionati {country}: {e}")
            return {}

    def get_italian_leagues(self) -> Dict:
        """Restituisce i campionati italiani"""
        return self.leagues_data.get("Italia", {})

    def get_serie_a_teams(self) -> List[Dict]:
        """Restituisce le squadre di Serie A"""
        italian_leagues = self.get_italian_leagues()
        serie_a = italian_leagues.get("Serie A") or italian_leagues.get("Serie_A")

        if serie_a and "squadre" in serie_a:
            return serie_a["squadre"]
        return []

    def get_serie_b_teams(self) -> List[Dict]:
        """Restituisce le squadre di Serie B"""
        italian_leagues = self.get_italian_leagues()
        serie_b = italian_leagues.get("Serie B") or italian_leagues.get("Serie_B")

        if serie_b and "squadre" in serie_b:
            return serie_b["squadre"]
        return []

    def get_serie_c_teams(self) -> List[Dict]:
        """Restituisce tutte le squadre di Serie C"""
        italian_leagues = self.get_italian_leagues()
        serie_c_teams = []

        # Serie C ha 3 gironi
        for girone in ["A", "B", "C"]:
            girone_key = f"Serie C - Girone {girone}"
            girone_alt_key = f"Serie_C_-_Girone_{girone}"

            girone_data = italian_leagues.get(girone_key) or italian_leagues.get(girone_alt_key)
            if girone_data and "squadre" in girone_data:
                serie_c_teams.extend(girone_data["squadre"])

        return serie_c_teams

    def get_serie_c_groups(self) -> Dict[str, List[str]]:
        """Restituisce i 3 gironi di Serie C separatamente"""
        italian_leagues = self.get_italian_leagues()
        serie_c_groups = {}

        # Serie C ha 3 gironi
        for girone in ["A", "B", "C"]:
            girone_key = f"Serie C - Girone {girone}"
            girone_alt_key = f"Serie_C_-_Girone_{girone}"

            girone_data = italian_leagues.get(girone_key) or italian_leagues.get(girone_alt_key)
            if girone_data and "squadre" in girone_data:
                # Estrai solo i nomi delle squadre
                team_names = [team["nome"] for team in girone_data["squadre"] if "nome" in team]
                serie_c_groups[f"Girone {girone}"] = team_names

        return serie_c_groups

    def get_all_italian_teams(self) -> List[Dict]:
        """Restituisce tutte le squadre italiane"""
        all_teams = []
        all_teams.extend(self.get_serie_a_teams())
        all_teams.extend(self.get_serie_b_teams())
        all_teams.extend(self.get_serie_c_teams())
        return all_teams

    def get_teams_by_level(self, level: int) -> List[Dict]:
        """Restituisce squadre per livello competizione"""
        if level == 1:
            return self.get_serie_a_teams()
        elif level == 2:
            return self.get_serie_b_teams()
        elif level == 3:
            return self.get_serie_c_teams()
        else:
            return []

    def get_team_by_name(self, team_name: str) -> Optional[Dict]:
        """Trova una squadra per nome"""
        all_teams = self.get_all_italian_teams()
        for team in all_teams:
            if team.get("nome", "").lower() == team_name.lower():
                return team
        return None

    def get_available_nations_for_names(self) -> List[str]:
        """Restituisce lista nazioni disponibili per nomi"""
        return list(self.player_names_data.keys())

    def get_names_for_nation(self, nation: str) -> Dict[str, List[str]]:
        """Restituisce nomi e cognomi per una nazione"""
        return self.player_names_data.get(nation, {"first_names": [], "last_names": []})

    def get_league_info(self, country: str, league_name: str) -> Optional[Dict]:
        """Restituisce informazioni su un campionato"""
        country_leagues = self.leagues_data.get(country, {})
        return country_leagues.get(league_name)

    def validate_data_integrity(self) -> bool:
        """Valida l'integrità dei dati caricati"""
        if not self._loaded:
            logger.error("Dati non ancora caricati")
            return False

        # Verifica nomi giocatori
        if not self.player_names_data:
            logger.error("Nomi giocatori non caricati")
            return False

        # Verifica campionati italiani
        italian_teams = self.get_all_italian_teams()
        if len(italian_teams) < 50:  # Dovrebbero essere circa 100 squadre
            logger.warning(f"Solo {len(italian_teams)} squadre italiane caricate")

        # Verifica Serie A
        serie_a_teams = self.get_serie_a_teams()
        if len(serie_a_teams) != 20:
            logger.warning(f"Serie A ha {len(serie_a_teams)} squadre invece di 20")

        logger.info("Validazione dati completata")
        return True

    def get_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche sui dati caricati"""
        # Statistiche base
        stats = {
            "nazioni_nomi": len(self.player_names_data),
            "paesi_campionati": len(self.leagues_data),
            "squadre_italiane": len(self.get_all_italian_teams()),
            "squadre_serie_a": len(self.get_serie_a_teams()),
            "squadre_serie_b": len(self.get_serie_b_teams()),
            "squadre_serie_c": len(self.get_serie_c_teams())
        }

        # Statistiche europee
        european_summary = self.get_european_leagues_summary()
        total_european_teams = 0
        uefa_qualified_count = 0

        qualified_teams = self.get_uefa_qualified_teams()
        for competition, teams in qualified_teams.items():
            uefa_qualified_count += len(teams)

        for country_info in european_summary.values():
            total_european_teams += country_info["first_division_teams"]

        stats.update({
            "paesi_europei": len(european_summary),
            "squadre_europee_totali": total_european_teams,
            "squadre_uefa_qualificate": uefa_qualified_count,
            "squadre_europee_non_uefa": total_european_teams - uefa_qualified_count,
        })

        # Totali complessivi
        total_teams = (stats["squadre_italiane"] + stats["squadre_europee_totali"])
        estimated_players = total_teams * 25

        stats.update({
            "squadre_totali": total_teams,
            "giocatori_stimati": estimated_players
        })

        # Dettagli per nazione (solo le prime 10 per evitare output troppo lungo)
        nation_details = {}
        for i, (nation, names_data) in enumerate(list(self.player_names_data.items())[:10]):
            first_names = len(names_data.get("first_names", []))
            last_names = len(names_data.get("last_names", []))
            nation_details[nation] = {"primi": first_names, "cognomi": last_names}

        stats["nazioni_dettagli"] = nation_details

        return stats

    def is_loaded(self) -> bool:
        """Verifica se i dati sono stati caricati"""
        return self._loaded

    @property
    def nation_names(self) -> Dict:
        """Proprietà per accedere ai dati dei nomi delle nazioni (compatibilità)"""
        return self.player_names_data

    def get_uefa_qualified_teams(self) -> Dict[str, Dict[str, List[Dict]]]:
        """Restituisce squadre qualificate per competizioni UEFA"""
        from ..competitions.coefficient_system import uefa_coefficient_system

        qualified_teams = {
            "Champions League": [],
            "Europa League": [],
            "Conference League": []
        }

        all_qualified = uefa_coefficient_system.get_all_uefa_qualified_teams()

        for country, competitions in all_qualified.items():
            country_leagues = self.leagues_data.get(country, {})

            # Trova prima divisione (livello 1)
            first_division = None
            first_division_name = None
            for league_name, league_data in country_leagues.items():
                if league_data.get("livello_competizione") == 1:
                    first_division = league_data.get("squadre", [])
                    first_division_name = league_name
                    break

            if not first_division:
                continue

            # Filtra solo le squadre che erano nella prima divisione la stagione precedente
            uefa_eligible_teams = self._filter_uefa_eligible_teams(first_division, first_division_name, country)

            # Ordina le squadre UEFA-eligible in base alla posizione della stagione precedente
            sorted_teams = self._sort_teams_by_previous_position(uefa_eligible_teams, country)

            # Per ogni competizione
            for competition, positions in competitions.items():
                for position in positions:
                    if position <= len(sorted_teams):
                        team_data = sorted_teams[position - 1].copy()
                        team_data["country"] = country
                        team_data["qualification_position"] = position
                        qualified_teams[competition].append(team_data)

        return qualified_teams

    def _filter_uefa_eligible_teams(self, teams: List[Dict], first_division_name: str, country: str) -> List[Dict]:
        """Filtra squadre che erano nella prima divisione la stagione precedente (UEFA-eligible)"""
        uefa_eligible = []

        for team in teams:
            pos = team.get("ultima_posizione_campionato_precedente")
            team_name = team.get("nome", "Unknown")

            # Se è un numero intero, significa che era nella prima divisione
            if isinstance(pos, int):
                uefa_eligible.append(team)
                continue

            # Se è una stringa, controlla se contiene riferimenti a divisioni inferiori
            if isinstance(pos, str):
                pos_lower = pos.lower()

                # Pattern per identificare squadre promosse (erano in divisioni inferiori)
                lower_division_patterns = [
                    'serie b', 'serie c', 'championship', 'ligue 2', '2. bundesliga',
                    'segunda división', 'liga portugal 2', 'eerste divisie', 'challenger pro league',
                    '2. liga', 'tff 1. lig', 'fortuna národní liga', 'scottish championship',
                    'challenge league', 'obos-ligaen', '1. division', 'prva liga',
                    'persha liha', 'playoff', 'vincitrice playoff'
                ]

                # Se contiene riferimenti a divisioni inferiori, NON è UEFA-eligible
                is_promoted = any(pattern in pos_lower for pattern in lower_division_patterns)

                if is_promoted:
                    logger.debug(f"{country}: {team_name} escluso dalle qualificazioni UEFA (promosso da: {pos})")
                    continue

                # Se contiene il nome della prima divisione, è UEFA-eligible
                first_div_patterns = self._get_first_division_patterns(first_division_name, country)
                is_from_first_division = any(pattern in pos_lower for pattern in first_div_patterns)

                if is_from_first_division:
                    uefa_eligible.append(team)
                    continue

                # Se non riusciamo a determinare, assumiamo che sia UEFA-eligible (conservativo)
                logger.warning(f"{country}: Impossibile determinare divisione precedente per {team_name}: {pos}")
                uefa_eligible.append(team)
            else:
                # Se manca il campo, assumiamo che sia UEFA-eligible (conservativo)
                logger.warning(f"{country}: Campo ultima_posizione_campionato_precedente mancante per {team_name}")
                uefa_eligible.append(team)

        logger.info(f"{country}: {len(uefa_eligible)}/{len(teams)} squadre UEFA-eligible nella prima divisione")
        return uefa_eligible

    def _get_first_division_patterns(self, first_division_name: str, country: str) -> List[str]:
        """Restituisce pattern per identificare la prima divisione di un paese"""
        patterns = [first_division_name.lower()]

        # Pattern specifici per paese
        country_patterns = {
            "Italia": ["serie a"],
            "Inghilterra": ["premier league", "premiership"],
            "Spagna": ["la liga", "primera división"],
            "Germania": ["bundesliga"],
            "Francia": ["ligue 1"],
            "Portogallo": ["primeira liga"],
            "Paesi Bassi": ["eredivisie"],
            "Belgio": ["jupiler pro league", "pro league"],
            "Austria": ["bundesliga"],
            "Turchia": ["süper lig"],
            "Repubblica Ceca": ["fortuna liga"],
            "Scozia": ["scottish premiership", "premiership"],
            "Svizzera": ["super league"],
            "Norvegia": ["eliteserien"],
            "Danimarca": ["superligaen"],
            "Serbia": ["superliga"],
            "Ucraina": ["ukrainian premier liha", "premier liha"],
            "Croazia": ["hnl"],
            "Grecia": ["super league greece"],
            "Israele": ["ligat ha'al"],
            "Cipro": ["protathlima cyta"],
            "Svezia": ["allsvenskan"],
            "Polonia": ["ekstraklasa"],
            "Ungheria": ["nemzeti bajnokság i"],
            "Romania": ["liga i"],
            "Bulgaria": ["parva profesionalna futbolna liga"],
            "Slovacchia": ["niké liga"],
            "Azerbaigian": ["azərbaycan premyer liqası"],
            "Slovenia": ["prvaliga"]
        }

        if country in country_patterns:
            patterns.extend(country_patterns[country])

        return patterns

    def _sort_teams_by_previous_position(self, teams: List[Dict], country: str) -> List[Dict]:
        """Ordina le squadre in base alla posizione della stagione precedente"""
        def get_sort_key(team):
            pos = team.get("ultima_posizione_campionato_precedente")

            # Se è un numero, usalo direttamente
            if isinstance(pos, int):
                return pos

            # Se è una stringa che contiene un numero, estrailo
            if isinstance(pos, str):
                # Cerca pattern come "16 (Primeira Liga)" o "14 (Eliteserien)"
                import re
                match = re.search(r'^(\d+)', pos.strip())
                if match:
                    return int(match.group(1))

                # Se non trova numeri, metti alla fine
                logger.warning(f"Posizione precedente non valida per {team.get('nome', 'Unknown')} ({country}): {pos}")
                return 999

            # Se manca il campo, metti alla fine
            logger.warning(f"Campo ultima_posizione_campionato_precedente mancante per {team.get('nome', 'Unknown')} ({country})")
            return 999

        try:
            sorted_teams = sorted(teams, key=get_sort_key)
            # Log delle prime 3 squadre ordinate
            top_3_info = [f"{t['nome']} (pos {t.get('ultima_posizione_campionato_precedente', 'N/A')})" for t in sorted_teams[:3]]
            logger.debug(f"{country}: Squadre ordinate per posizione precedente - Prime 3: {top_3_info}")
            return sorted_teams
        except Exception as e:
            logger.error(f"Errore ordinamento squadre {country}: {e}")
            return teams  # Fallback all'ordine originale

    def get_all_european_leagues(self) -> Dict[str, Dict]:
        """Restituisce tutti i campionati europei caricati"""
        european_leagues = {}
        for country, leagues in self.leagues_data.items():
            if country != "Italia":  # Esclude Italia che ha gestione separata
                european_leagues[country] = leagues
        return european_leagues

    def get_european_teams_count(self) -> int:
        """Restituisce numero totale squadre europee"""
        count = 0
        european_leagues = self.get_all_european_leagues()

        for country_leagues in european_leagues.values():
            for league_data in country_leagues.values():
                if league_data.get("livello_competizione") == 1:  # Solo prime divisioni
                    count += len(league_data.get("squadre", []))

        return count

    def get_european_leagues_summary(self) -> Dict[str, Dict]:
        """Restituisce riassunto campionati europei caricati"""
        summary = {}
        european_leagues = self.get_all_european_leagues()

        for country, leagues in european_leagues.items():
            country_info = {
                "leagues_count": len(leagues),
                "first_division_teams": 0,
                "total_teams": 0
            }

            for league_name, league_data in leagues.items():
                teams_count = len(league_data.get("squadre", []))
                country_info["total_teams"] += teams_count

                if league_data.get("livello_competizione") == 1:
                    country_info["first_division_teams"] = teams_count
                    country_info["first_division_name"] = league_name

            summary[country] = country_info

        return summary

    def get_non_uefa_qualified_european_teams(self) -> List[Dict]:
        """Restituisce squadre europee non qualificate UEFA"""
        # Ottieni squadre qualificate UEFA
        qualified_teams = self.get_uefa_qualified_teams()
        uefa_team_names = set()
        for competition, teams in qualified_teams.items():
            for team_data in teams:
                uefa_team_names.add(team_data["nome"])

        # Ottieni tutte le squadre europee
        european_leagues = self.get_all_european_leagues()
        non_qualified_teams = []

        for country, leagues in european_leagues.items():
            for league_name, league_data in leagues.items():
                if league_data.get("livello_competizione") == 1:  # Solo prime divisioni
                    teams = league_data.get("squadre", [])
                    for team_data in teams:
                        team_name = team_data["nome"]
                        if team_name not in uefa_team_names:
                            # Aggiungi info paese
                            team_copy = team_data.copy()
                            team_copy["country"] = country
                            team_copy["league"] = league_name
                            non_qualified_teams.append(team_copy)

        return non_qualified_teams

# Istanza globale del data loader
data_loader = DataLoader()
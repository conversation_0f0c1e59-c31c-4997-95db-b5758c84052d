"""
Pannello panoramica squadra con informazioni principali
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QGroupBox, QGridLayout, QProgressBar, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap

from ..components.info_display import TeamInfoDisplay
from ..components.action_buttons import ActionButtons
from ...core.config import (COLOR_SURFACE, COLOR_PRIMARY, COLOR_BORDER,
                           COLOR_SUCCESS, COLOR_WARNING, MAIN_FONT_FAMILY,
                           HEADER_FONT_SIZE)


class TeamOverviewPanel(QWidget):
    """Pannello panoramica completa della squadra"""

    action_requested = pyqtSignal(str, dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.club_data = {}
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Header squadra
        self.create_team_header(main_layout)

        # Layout principale a 2 colonne
        content_layout = QHBoxLayout()

        # Colonna sinistra - Info principali
        left_column = QVBoxLayout()
        self.create_team_info_section(left_column)
        self.create_facilities_section(left_column)

        # Colonna destra - Statistiche e status
        right_column = QVBoxLayout()
        self.create_season_stats_section(right_column)
        self.create_quick_actions_section(right_column)

        # Aggiungi colonne al layout
        content_layout.addLayout(left_column, 1)
        content_layout.addLayout(right_column, 1)

        main_layout.addLayout(content_layout)
        main_layout.addStretch()

    def create_team_header(self, parent_layout):
        """Crea l'header della squadra"""
        header_widget = QWidget()
        header_widget.setFixedHeight(80)
        header_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {COLOR_SURFACE};
                border: 2px solid {COLOR_PRIMARY};
                border-radius: 10px;
                margin-bottom: 10px;
            }}
        """)

        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 15, 20, 15)

        # Logo squadra (placeholder)
        logo_label = QLabel("🏆")
        logo_label.setFont(QFont(MAIN_FONT_FAMILY, 32))
        logo_label.setFixedSize(50, 50)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(logo_label)

        # Nome e info squadra
        info_layout = QVBoxLayout()

        self.team_name_label = QLabel("Nome Squadra")
        self.team_name_label.setFont(QFont(MAIN_FONT_FAMILY, 18, QFont.Weight.Bold))
        self.team_name_label.setStyleSheet(f"color: {COLOR_PRIMARY};")

        self.team_info_label = QLabel("Informazioni aggiuntive")
        self.team_info_label.setStyleSheet("color: #666;")

        info_layout.addWidget(self.team_name_label)
        info_layout.addWidget(self.team_info_label)

        header_layout.addLayout(info_layout)
        header_layout.addStretch()

        # Status rapido
        self.create_quick_status(header_layout)

        parent_layout.addWidget(header_widget)

    def create_quick_status(self, parent_layout):
        """Crea indicatori di status rapidi"""
        status_layout = QVBoxLayout()

        # Budget status
        budget_label = QLabel("Budget")
        budget_label.setFont(QFont(MAIN_FONT_FAMILY, 9, QFont.Weight.Bold))
        self.budget_value_label = QLabel("€0")
        self.budget_value_label.setStyleSheet(f"color: {COLOR_SUCCESS}; font-weight: bold;")

        # Morale status
        morale_label = QLabel("Morale")
        morale_label.setFont(QFont(MAIN_FONT_FAMILY, 9, QFont.Weight.Bold))
        self.morale_value_label = QLabel("Alto")
        self.morale_value_label.setStyleSheet(f"color: {COLOR_SUCCESS}; font-weight: bold;")

        status_layout.addWidget(budget_label)
        status_layout.addWidget(self.budget_value_label)
        status_layout.addWidget(morale_label)
        status_layout.addWidget(self.morale_value_label)

        parent_layout.addLayout(status_layout)

    def create_team_info_section(self, parent_layout):
        """Crea la sezione informazioni squadra"""
        info_group = QGroupBox("Informazioni Squadra")
        info_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }}
        """)

        # Usa il componente TeamInfoDisplay
        self.team_info_display = TeamInfoDisplay()

        info_layout = QVBoxLayout(info_group)
        info_layout.addWidget(self.team_info_display)

        parent_layout.addWidget(info_group)

    def create_facilities_section(self, parent_layout):
        """Crea la sezione strutture"""
        facilities_group = QGroupBox("Strutture")
        facilities_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }}
        """)

        facilities_layout = QGridLayout(facilities_group)
        facilities_layout.setSpacing(10)

        # Strutture con barre di progresso
        self.facilities_bars = {}
        facilities = [
            ("Allenamento", "strutture_allenamento"),
            ("Giovanili", "strutture_giovanili"),
            ("Reclutamento", "reclutamento_giovanile"),
            ("Stadio", "condizione_stadio")
        ]

        for i, (name, key) in enumerate(facilities):
            label = QLabel(name)
            label.setFont(QFont(MAIN_FONT_FAMILY, 9, QFont.Weight.Bold))

            progress_bar = QProgressBar()
            progress_bar.setMaximum(10)
            progress_bar.setTextVisible(True)
            progress_bar.setFormat("%v/10")
            progress_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 1px solid {COLOR_BORDER};
                    border-radius: 4px;
                    text-align: center;
                }}
                QProgressBar::chunk {{
                    background-color: {COLOR_PRIMARY};
                    border-radius: 3px;
                }}
            """)

            facilities_layout.addWidget(label, i, 0)
            facilities_layout.addWidget(progress_bar, i, 1)

            self.facilities_bars[key] = progress_bar

        parent_layout.addWidget(facilities_group)

    def create_season_stats_section(self, parent_layout):
        """Crea la sezione statistiche stagione"""
        stats_group = QGroupBox("Statistiche Stagione")
        stats_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }}
        """)

        stats_layout = QGridLayout(stats_group)
        stats_layout.setSpacing(15)

        # Statistiche placeholder
        stats_data = [
            ("Partite", "0"),
            ("Vittorie", "0"),
            ("Pareggi", "0"),
            ("Sconfitte", "0"),
            ("Gol Fatti", "0"),
            ("Gol Subiti", "0"),
            ("Posizione", "N/A"),
            ("Punti", "0")
        ]

        for i, (label, value) in enumerate(stats_data):
            row = i // 2
            col = (i % 2) * 2

            label_widget = QLabel(f"{label}:")
            label_widget.setFont(QFont(MAIN_FONT_FAMILY, 9, QFont.Weight.Bold))

            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"color: {COLOR_PRIMARY}; font-weight: bold;")

            stats_layout.addWidget(label_widget, row, col)
            stats_layout.addWidget(value_widget, row, col + 1)

        parent_layout.addWidget(stats_group)

    def create_quick_actions_section(self, parent_layout):
        """Crea la sezione azioni rapide"""
        actions_group = QGroupBox("Azioni Rapide")
        actions_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }}
        """)

        actions_layout = QVBoxLayout(actions_group)

        # Pulsanti azioni
        self.action_buttons = ActionButtons('vertical')
        self.action_buttons.add_button("Vedi Rosa", "view_squad", "primary")
        self.action_buttons.add_button("Prossima Partita", "next_match", "success")
        self.action_buttons.add_button("Mercato", "transfer_market", "warning")
        self.action_buttons.add_button("Finanze", "finances", "secondary")

        # Connetti azioni
        self.action_buttons.action_triggered.connect(self.action_requested.emit)

        actions_layout.addWidget(self.action_buttons)
        actions_layout.addStretch()

        parent_layout.addWidget(actions_group)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data
        self.club_data = game_data.get('selected_club', {})
        self.refresh_display()

    def refresh_display(self):
        """Aggiorna tutti i componenti con i nuovi dati"""
        if not self.club_data:
            return

        # Aggiorna header
        self.team_name_label.setText(self.club_data.get('nome', 'Nome Squadra'))

        city = self.club_data.get('citta', '')
        stadium = self.club_data.get('stadio', '')
        self.team_info_label.setText(f"{city} - {stadium}")

        # Aggiorna budget
        budget = self.club_data.get('budget_trasferimenti_eur', 0)
        self.budget_value_label.setText(f"€{budget:,.0f}")

        # Aggiorna info display
        self.team_info_display.set_data(self.club_data)

        # Aggiorna barre strutture
        for key, progress_bar in self.facilities_bars.items():
            value = self.club_data.get(key, 0)
            progress_bar.setValue(value)

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'team_overview',
            'club_data': self.club_data,
            'game_data': self.game_data
        }
"""
Pannello calendario partite e eventi
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QCalendarWidget, QGroupBox, QListWidget, QListWidgetItem,
                            QTabWidget, QTextEdit, QPushButton)
from PyQt6.QtCore import Qt, pyqtSignal, QDate
from PyQt6.QtGui import QFont, QTextCharFormat

from ..components.info_display import InfoDisplay
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           COLOR_SUCCESS, COLOR_WARNING, MAIN_FONT_FAMILY,
                           HEADER_FONT_SIZE)


class CalendarPanel(QWidget):
    """Pannello calendario con partite e eventi"""

    date_selected = pyqtSignal(QDate)
    event_selected = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.events_data = {}
        self.selected_date = QDate.currentDate()
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        self.create_panel_header(main_layout)

        # Layout principale
        content_layout = QHBoxLayout()

        # Lato sinistro - Calendario
        self.create_calendar_section(content_layout)

        # Lato destro - Eventi del giorno
        self.create_events_section(content_layout)

        main_layout.addLayout(content_layout)

    def create_panel_header(self, parent_layout):
        """Crea l'header del pannello"""
        header_layout = QHBoxLayout()

        # Titolo
        title_label = QLabel("Calendario Stagione")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Data corrente
        self.current_date_label = QLabel("1 Luglio 2025")
        self.current_date_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                color: {COLOR_SUCCESS};
                padding: 5px 10px;
                border: 1px solid {COLOR_SUCCESS};
                border-radius: 4px;
            }}
        """)
        header_layout.addWidget(self.current_date_label)

        parent_layout.addLayout(header_layout)

    def create_calendar_section(self, parent_layout):
        """Crea la sezione calendario"""
        calendar_group = QGroupBox("Calendario")
        calendar_layout = QVBoxLayout(calendar_group)

        # Widget calendario
        self.calendar = QCalendarWidget()
        self.calendar.setMinimumSize(350, 250)
        self.calendar.clicked.connect(self.on_date_selected)

        # Configura formato date speciali
        self.setup_calendar_formats()

        calendar_layout.addWidget(self.calendar)

        # Controlli navigazione
        nav_layout = QHBoxLayout()
        
        prev_month_btn = QPushButton("← Mese Prec.")
        prev_month_btn.clicked.connect(self.previous_month)
        nav_layout.addWidget(prev_month_btn)

        today_btn = QPushButton("Oggi")
        today_btn.clicked.connect(self.go_to_today)
        nav_layout.addWidget(today_btn)

        next_month_btn = QPushButton("Mese Succ. →")
        next_month_btn.clicked.connect(self.next_month)
        nav_layout.addWidget(next_month_btn)

        calendar_layout.addLayout(nav_layout)

        parent_layout.addWidget(calendar_group)

    def create_events_section(self, parent_layout):
        """Crea la sezione eventi"""
        events_group = QGroupBox("Eventi del Giorno")
        events_layout = QVBoxLayout(events_group)

        # Data selezionata
        self.selected_date_label = QLabel("Seleziona una data")
        self.selected_date_label.setFont(QFont(MAIN_FONT_FAMILY, 12, QFont.Weight.Bold))
        self.selected_date_label.setStyleSheet(f"color: {COLOR_PRIMARY}; padding: 5px;")
        events_layout.addWidget(self.selected_date_label)

        # Tab eventi
        self.events_tab = QTabWidget()

        # Tab Partite
        self.create_matches_tab()

        # Tab Altri Eventi
        self.create_other_events_tab()

        events_layout.addWidget(self.events_tab)

        # Dettagli evento selezionato
        details_group = QGroupBox("Dettagli")
        details_layout = QVBoxLayout(details_group)

        self.event_details = InfoDisplay()
        details_layout.addWidget(self.event_details)

        events_layout.addWidget(details_group)

        parent_layout.addWidget(events_group)

    def create_matches_tab(self):
        """Crea la tab partite"""
        matches_widget = QWidget()
        layout = QVBoxLayout(matches_widget)

        self.matches_list = QListWidget()
        self.matches_list.itemClicked.connect(self.on_match_selected)
        layout.addWidget(self.matches_list)

        self.events_tab.addTab(matches_widget, "⚽ Partite")

    def create_other_events_tab(self):
        """Crea la tab altri eventi"""
        events_widget = QWidget()
        layout = QVBoxLayout(events_widget)

        self.other_events_list = QListWidget()
        self.other_events_list.itemClicked.connect(self.on_event_selected)
        layout.addWidget(self.other_events_list)

        self.events_tab.addTab(events_widget, "📅 Eventi")

    def setup_calendar_formats(self):
        """Configura i formati per le date speciali"""
        # Formato per partite
        self.match_format = QTextCharFormat()
        self.match_format.setBackground(Qt.GlobalColor.lightBlue)
        self.match_format.setForeground(Qt.GlobalColor.darkBlue)

        # Formato per eventi importanti
        self.event_format = QTextCharFormat()
        self.event_format.setBackground(Qt.GlobalColor.yellow)
        self.event_format.setForeground(Qt.GlobalColor.darkRed)

        # Formato per mercato
        self.market_format = QTextCharFormat()
        self.market_format.setBackground(Qt.GlobalColor.lightGreen)
        self.market_format.setForeground(Qt.GlobalColor.darkGreen)

    def on_date_selected(self, date):
        """Gestisce la selezione di una data"""
        self.selected_date = date
        self.selected_date_label.setText(date.toString("dddd, d MMMM yyyy"))
        self.load_events_for_date(date)
        self.date_selected.emit(date)

    def on_match_selected(self, item):
        """Gestisce la selezione di una partita"""
        match_data = item.data(Qt.ItemDataRole.UserRole)
        if match_data:
            self.show_match_details(match_data)
            self.event_selected.emit(match_data)

    def on_event_selected(self, item):
        """Gestisce la selezione di un evento"""
        event_data = item.data(Qt.ItemDataRole.UserRole)
        if event_data:
            self.show_event_details(event_data)
            self.event_selected.emit(event_data)

    def load_events_for_date(self, date):
        """Carica gli eventi per una data specifica"""
        date_str = date.toString("yyyy-MM-dd")
        
        # Pulisce le liste
        self.matches_list.clear()
        self.other_events_list.clear()

        # Carica partite
        matches = self.get_matches_for_date(date_str)
        for match in matches:
            item = QListWidgetItem(f"{match['casa']} vs {match['trasferta']}")
            item.setData(Qt.ItemDataRole.UserRole, match)
            self.matches_list.addItem(item)

        # Carica altri eventi
        events = self.get_events_for_date(date_str)
        for event in events:
            item = QListWidgetItem(f"{event['tipo']}: {event['descrizione']}")
            item.setData(Qt.ItemDataRole.UserRole, event)
            self.other_events_list.addItem(item)

        # Aggiorna dettagli
        if not matches and not events:
            self.event_details.clear_content()
            no_events = QLabel("Nessun evento programmato")
            no_events.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_events.setStyleSheet("color: #999; font-style: italic; padding: 20px;")
            self.event_details.add_custom_widget(no_events)

    def get_matches_for_date(self, date_str):
        """Ottiene le partite per una data"""
        # Dati di esempio
        sample_matches = {
            "2025-07-15": [
                {"casa": "Juventus", "trasferta": "Real Madrid", "competizione": "Amichevole", "orario": "20:45"}
            ],
            "2025-08-20": [
                {"casa": "Juventus", "trasferta": "Como", "competizione": "Serie A", "orario": "20:45"}
            ]
        }
        return sample_matches.get(date_str, [])

    def get_events_for_date(self, date_str):
        """Ottiene gli eventi per una data"""
        # Dati di esempio
        sample_events = {
            "2025-07-01": [
                {"tipo": "Mercato", "descrizione": "Apertura sessione estiva", "importanza": "Alta"}
            ],
            "2025-08-31": [
                {"tipo": "Mercato", "descrizione": "Chiusura sessione estiva", "importanza": "Alta"}
            ]
        }
        return sample_events.get(date_str, [])

    def show_match_details(self, match_data):
        """Mostra i dettagli di una partita"""
        self.event_details.set_data(match_data)

    def show_event_details(self, event_data):
        """Mostra i dettagli di un evento"""
        self.event_details.set_data(event_data)

    def previous_month(self):
        """Va al mese precedente"""
        current_date = self.calendar.selectedDate()
        new_date = current_date.addMonths(-1)
        self.calendar.setSelectedDate(new_date)

    def next_month(self):
        """Va al mese successivo"""
        current_date = self.calendar.selectedDate()
        new_date = current_date.addMonths(1)
        self.calendar.setSelectedDate(new_date)

    def go_to_today(self):
        """Va alla data odierna"""
        today = QDate.currentDate()
        self.calendar.setSelectedDate(today)
        self.on_date_selected(today)

    def highlight_special_dates(self):
        """Evidenzia le date speciali nel calendario"""
        # Evidenzia date con partite
        match_dates = ["2025-07-15", "2025-08-20"]
        for date_str in match_dates:
            date = QDate.fromString(date_str, "yyyy-MM-dd")
            self.calendar.setDateTextFormat(date, self.match_format)

        # Evidenzia eventi mercato
        market_dates = ["2025-07-01", "2025-08-31"]
        for date_str in market_dates:
            date = QDate.fromString(date_str, "yyyy-MM-dd")
            self.calendar.setDateTextFormat(date, self.market_format)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data
        self.highlight_special_dates()

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'calendar',
            'selected_date': self.selected_date.toString("yyyy-MM-dd"),
            'events_count': len(self.events_data)
        }

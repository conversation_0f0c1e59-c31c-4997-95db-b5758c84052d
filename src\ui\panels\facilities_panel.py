"""
Pannello gestione strutture e infrastrutture del club
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QGroupBox, QProgressBar, QGridLayout, QPushButton,
                            QTabWidget, QTextEdit, QSpinBox, QSlider)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..components.action_buttons import ActionButtons
from ..components.info_display import InfoDisplay
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           COLOR_SUCCESS, COLOR_WARNING, MAIN_FONT_FAMILY,
                           HEADER_FONT_SIZE)


class FacilitiesPanel(QWidget):
    """Pannello per gestione strutture del club"""

    upgrade_requested = pyqtSignal(str, dict)
    investment_made = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.club_data = {}
        self.facilities_data = {}
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        self.create_panel_header(main_layout)

        # Tab widget principale
        self.tab_widget = QTabWidget()

        # Tab Strutture Allenamento
        self.create_training_facilities_tab()

        # Tab Settore Giovanile
        self.create_youth_facilities_tab()

        # Tab Stadio
        self.create_stadium_tab()

        # Tab Investimenti
        self.create_investments_tab()

        main_layout.addWidget(self.tab_widget)

    def create_panel_header(self, parent_layout):
        """Crea l'header del pannello"""
        header_layout = QHBoxLayout()

        # Titolo
        title_label = QLabel("Gestione Strutture")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Budget disponibile
        self.budget_label = QLabel("Budget: €0")
        self.budget_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                color: {COLOR_SUCCESS};
                padding: 5px 10px;
                border: 1px solid {COLOR_SUCCESS};
                border-radius: 4px;
            }}
        """)
        header_layout.addWidget(self.budget_label)

        parent_layout.addLayout(header_layout)

    def create_training_facilities_tab(self):
        """Crea la tab strutture allenamento"""
        training_widget = QWidget()
        layout = QVBoxLayout(training_widget)

        # Panoramica strutture
        overview_group = QGroupBox("Panoramica Strutture Allenamento")
        overview_layout = QGridLayout(overview_group)

        facilities = [
            ("Campo Principale", "campo_principale", 8),
            ("Campi Secondari", "campi_secondari", 6),
            ("Palestra", "palestra", 7),
            ("Centro Medico", "centro_medico", 5),
            ("Spogliatoi", "spogliatoi", 9),
            ("Sala Video", "sala_video", 4)
        ]

        self.training_bars = {}
        for i, (name, key, current_level) in enumerate(facilities):
            row = i // 2
            col = (i % 2) * 3

            # Nome struttura
            name_label = QLabel(name)
            name_label.setFont(QFont(MAIN_FONT_FAMILY, 10, QFont.Weight.Bold))
            overview_layout.addWidget(name_label, row, col)

            # Barra livello
            progress_bar = QProgressBar()
            progress_bar.setMaximum(10)
            progress_bar.setValue(current_level)
            progress_bar.setFormat(f"{current_level}/10")
            progress_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 1px solid {COLOR_BORDER};
                    border-radius: 4px;
                    text-align: center;
                }}
                QProgressBar::chunk {{
                    background-color: {COLOR_PRIMARY};
                    border-radius: 3px;
                }}
            """)
            overview_layout.addWidget(progress_bar, row, col + 1)

            # Pulsante upgrade
            upgrade_btn = QPushButton("Migliora")
            upgrade_btn.setFixedSize(80, 25)
            upgrade_btn.clicked.connect(lambda checked, k=key: self.request_upgrade(k))
            overview_layout.addWidget(upgrade_btn, row, col + 2)

            self.training_bars[key] = progress_bar

        layout.addWidget(overview_group)

        # Benefici strutture
        benefits_group = QGroupBox("Benefici Attuali")
        benefits_layout = QVBoxLayout(benefits_group)

        self.training_benefits = InfoDisplay()
        benefits_layout.addWidget(self.training_benefits)

        layout.addWidget(benefits_group)
        layout.addStretch()

        self.tab_widget.addTab(training_widget, "🏋️ Allenamento")

    def create_youth_facilities_tab(self):
        """Crea la tab settore giovanile"""
        youth_widget = QWidget()
        layout = QVBoxLayout(youth_widget)

        # Strutture giovanili
        youth_group = QGroupBox("Settore Giovanile")
        youth_layout = QGridLayout(youth_group)

        youth_facilities = [
            ("Accademia", "accademia", 6),
            ("Campi Giovanili", "campi_giovanili", 5),
            ("Dormitori", "dormitori", 4),
            ("Scuola", "scuola", 7),
            ("Staff Tecnico", "staff_giovanile", 8),
            ("Scouting", "scouting_giovanile", 5)
        ]

        self.youth_bars = {}
        for i, (name, key, current_level) in enumerate(youth_facilities):
            row = i // 2
            col = (i % 2) * 3

            name_label = QLabel(name)
            name_label.setFont(QFont(MAIN_FONT_FAMILY, 10, QFont.Weight.Bold))
            youth_layout.addWidget(name_label, row, col)

            progress_bar = QProgressBar()
            progress_bar.setMaximum(10)
            progress_bar.setValue(current_level)
            progress_bar.setFormat(f"{current_level}/10")
            progress_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 1px solid {COLOR_BORDER};
                    border-radius: 4px;
                    text-align: center;
                }}
                QProgressBar::chunk {{
                    background-color: {COLOR_SUCCESS};
                    border-radius: 3px;
                }}
            """)
            youth_layout.addWidget(progress_bar, row, col + 1)

            upgrade_btn = QPushButton("Migliora")
            upgrade_btn.setFixedSize(80, 25)
            upgrade_btn.clicked.connect(lambda checked, k=key: self.request_upgrade(k))
            youth_layout.addWidget(upgrade_btn, row, col + 2)

            self.youth_bars[key] = progress_bar

        layout.addWidget(youth_group)

        # Statistiche produzione giovani
        stats_group = QGroupBox("Statistiche Produzione")
        stats_layout = QVBoxLayout(stats_group)

        self.youth_stats = InfoDisplay()
        stats_layout.addWidget(self.youth_stats)

        layout.addWidget(stats_group)
        layout.addStretch()

        self.tab_widget.addTab(youth_widget, "👶 Giovanile")

    def create_stadium_tab(self):
        """Crea la tab stadio"""
        stadium_widget = QWidget()
        layout = QVBoxLayout(stadium_widget)

        # Info stadio
        stadium_info_group = QGroupBox("Informazioni Stadio")
        stadium_info_layout = QVBoxLayout(stadium_info_group)

        self.stadium_info = InfoDisplay()
        stadium_info_layout.addWidget(self.stadium_info)

        layout.addWidget(stadium_info_group)

        # Miglioramenti stadio
        improvements_group = QGroupBox("Miglioramenti Disponibili")
        improvements_layout = QGridLayout(improvements_group)

        stadium_improvements = [
            ("Capienza", "Aumenta posti disponibili", "€5,000,000"),
            ("Illuminazione", "Migliora sistema luci", "€500,000"),
            ("Parcheggi", "Espandi area parcheggio", "€1,000,000"),
            ("Hospitality", "Migliora servizi VIP", "€2,000,000"),
            ("Sicurezza", "Potenzia sistemi sicurezza", "€800,000"),
            ("Accessibilità", "Migliora accesso disabili", "€600,000")
        ]

        for i, (name, description, cost) in enumerate(stadium_improvements):
            row = i // 2
            col = (i % 2) * 2

            improvement_widget = QWidget()
            improvement_layout = QVBoxLayout(improvement_widget)
            improvement_layout.setContentsMargins(5, 5, 5, 5)

            name_label = QLabel(name)
            name_label.setFont(QFont(MAIN_FONT_FAMILY, 10, QFont.Weight.Bold))
            improvement_layout.addWidget(name_label)

            desc_label = QLabel(description)
            desc_label.setStyleSheet("color: #666; font-size: 9px;")
            improvement_layout.addWidget(desc_label)

            cost_label = QLabel(cost)
            cost_label.setStyleSheet(f"color: {COLOR_WARNING}; font-weight: bold;")
            improvement_layout.addWidget(cost_label)

            invest_btn = QPushButton("Investi")
            invest_btn.setFixedHeight(25)
            invest_btn.clicked.connect(lambda checked, n=name: self.invest_in_stadium(n))
            improvement_layout.addWidget(invest_btn)

            improvement_widget.setStyleSheet(f"""
                QWidget {{
                    border: 1px solid {COLOR_BORDER};
                    border-radius: 4px;
                    background-color: {COLOR_SURFACE};
                }}
            """)

            improvements_layout.addWidget(improvement_widget, row, col)

        layout.addWidget(improvements_group)
        layout.addStretch()

        self.tab_widget.addTab(stadium_widget, "🏟️ Stadio")

    def create_investments_tab(self):
        """Crea la tab investimenti"""
        investments_widget = QWidget()
        layout = QVBoxLayout(investments_widget)

        # Pianificazione investimenti
        planning_group = QGroupBox("Pianificazione Investimenti")
        planning_layout = QVBoxLayout(planning_group)

        # Budget allocator
        budget_layout = QHBoxLayout()
        budget_layout.addWidget(QLabel("Budget da allocare:"))

        self.investment_slider = QSlider(Qt.Orientation.Horizontal)
        self.investment_slider.setMinimum(0)
        self.investment_slider.setMaximum(10000000)  # 10M max
        self.investment_slider.setValue(1000000)  # 1M default
        self.investment_slider.valueChanged.connect(self.update_investment_amount)
        budget_layout.addWidget(self.investment_slider)

        self.investment_amount_label = QLabel("€1,000,000")
        self.investment_amount_label.setStyleSheet(f"color: {COLOR_PRIMARY}; font-weight: bold;")
        budget_layout.addWidget(self.investment_amount_label)

        planning_layout.addLayout(budget_layout)

        # Priorità investimenti
        priorities_layout = QGridLayout()
        priorities_layout.addWidget(QLabel("Priorità:"), 0, 0)

        self.priority_combos = {}
        priorities = ["Allenamento", "Giovanile", "Stadio", "Medico"]
        for i, priority in enumerate(priorities):
            priorities_layout.addWidget(QLabel(f"{priority}:"), i+1, 0)
            combo = QSpinBox()
            combo.setMinimum(1)
            combo.setMaximum(4)
            combo.setValue(i+1)
            self.priority_combos[priority] = combo
            priorities_layout.addWidget(combo, i+1, 1)

        planning_layout.addLayout(priorities_layout)

        # Pulsante conferma investimenti
        confirm_btn = QPushButton("Conferma Piano Investimenti")
        confirm_btn.clicked.connect(self.confirm_investments)
        planning_layout.addWidget(confirm_btn)

        layout.addWidget(planning_group)

        # Storico investimenti
        history_group = QGroupBox("Storico Investimenti")
        history_layout = QVBoxLayout(history_group)

        self.investment_history = QTextEdit()
        self.investment_history.setReadOnly(True)
        self.investment_history.setMaximumHeight(150)
        self.investment_history.setPlaceholderText("Nessun investimento effettuato ancora...")
        history_layout.addWidget(self.investment_history)

        layout.addWidget(history_group)
        layout.addStretch()

        self.tab_widget.addTab(investments_widget, "💰 Investimenti")

    def request_upgrade(self, facility_key):
        """Richiede l'upgrade di una struttura"""
        upgrade_data = {
            'facility': facility_key,
            'current_level': self.get_facility_level(facility_key),
            'cost': self.calculate_upgrade_cost(facility_key)
        }
        self.upgrade_requested.emit(facility_key, upgrade_data)

    def invest_in_stadium(self, improvement_name):
        """Investe in un miglioramento dello stadio"""
        investment_data = {
            'type': 'stadium',
            'improvement': improvement_name,
            'cost': self.get_stadium_improvement_cost(improvement_name)
        }
        self.investment_made.emit(investment_data)

    def update_investment_amount(self, value):
        """Aggiorna l'importo dell'investimento"""
        self.investment_amount_label.setText(f"€{value:,.0f}")

    def confirm_investments(self):
        """Conferma il piano di investimenti"""
        investment_plan = {
            'amount': self.investment_slider.value(),
            'priorities': {key: combo.value() for key, combo in self.priority_combos.items()}
        }
        self.investment_made.emit(investment_plan)

    def get_facility_level(self, facility_key):
        """Ottiene il livello corrente di una struttura"""
        return self.facilities_data.get(facility_key, 5)

    def calculate_upgrade_cost(self, facility_key):
        """Calcola il costo di upgrade"""
        base_costs = {
            'campo_principale': 500000,
            'campi_secondari': 300000,
            'palestra': 200000,
            'centro_medico': 400000,
            'spogliatoi': 150000,
            'sala_video': 100000
        }
        base_cost = base_costs.get(facility_key, 250000)
        current_level = self.get_facility_level(facility_key)
        return base_cost * (current_level + 1)

    def get_stadium_improvement_cost(self, improvement_name):
        """Ottiene il costo di un miglioramento stadio"""
        costs = {
            'Capienza': 5000000,
            'Illuminazione': 500000,
            'Parcheggi': 1000000,
            'Hospitality': 2000000,
            'Sicurezza': 800000,
            'Accessibilità': 600000
        }
        return costs.get(improvement_name, 1000000)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data
        self.club_data = game_data.get('selected_club', {})
        self.update_display()

    def update_display(self):
        """Aggiorna la visualizzazione"""
        if not self.club_data:
            return

        # Aggiorna budget
        budget = self.club_data.get('budget_trasferimenti_eur', 0)
        self.budget_label.setText(f"Budget: €{budget:,.0f}")

        # Aggiorna info stadio
        stadium_info = {
            'Nome': self.club_data.get('stadio', 'N/A'),
            'Capienza': f"{self.club_data.get('capienza_stadio', 0):,} posti",
            'Condizione': f"{self.club_data.get('condizione_stadio', 5)}/10"
        }
        self.stadium_info.set_data(stadium_info)

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'facilities',
            'facilities_data': self.facilities_data,
            'investment_amount': self.investment_slider.value()
        }

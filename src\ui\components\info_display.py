"""
Display informazioni riutilizzabile senza card, layout pulito
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QFrame, QGridLayout, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap

from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_TEXT,
                           COLOR_PRIMARY, COLOR_SECONDARY, MAIN_FONT_FAMILY,
                           MAIN_FONT_SIZE, HEADER_FONT_SIZE)


class InfoDisplay(QWidget):
    """Display informazioni pulito senza card"""

    info_clicked = pyqtSignal(str, object)  # tipo, dati

    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.info_data = {}
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia base"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(10)

        # Titolo se presente
        if self.title:
            self.create_title()

        # Container contenuto
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(8)

        self.main_layout.addWidget(self.content_widget)

        # Stile base
        self.setStyleSheet(f"""
            InfoDisplay {{
                background-color: transparent;
            }}
            QLabel {{
                color: {COLOR_TEXT};
                font-family: {MAIN_FONT_FAMILY};
            }}
        """)

    def create_title(self):
        """Crea il titolo del display"""
        title_label = QLabel(self.title)
        title_label.setFont(QFont(MAIN_FONT_FAMILY, HEADER_FONT_SIZE, QFont.Weight.Bold))
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {COLOR_PRIMARY};
                padding-bottom: 8px;
                border-bottom: 2px solid {COLOR_BORDER};
                margin-bottom: 10px;
            }}
        """)
        self.main_layout.addWidget(title_label)

    def add_info_row(self, label, value, bold_label=True):
        """Aggiunge una riga di informazioni"""
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(0, 0, 0, 0)

        # Label
        label_widget = QLabel(f"{label}:")
        if bold_label:
            label_widget.setFont(QFont(MAIN_FONT_FAMILY, MAIN_FONT_SIZE, QFont.Weight.Bold))
        label_widget.setMinimumWidth(120)
        label_widget.setStyleSheet(f"color: {COLOR_SECONDARY};")

        # Valore
        value_widget = QLabel(str(value))
        value_widget.setWordWrap(True)

        row_layout.addWidget(label_widget)
        row_layout.addWidget(value_widget, 1)

        self.content_layout.addWidget(row_widget)
        return row_widget

    def add_info_grid(self, data, columns=2):
        """Aggiunge informazioni in formato griglia"""
        grid_widget = QWidget()
        grid_layout = QGridLayout(grid_widget)
        grid_layout.setSpacing(8)

        row = 0
        col = 0

        for key, value in data.items():
            # Label
            label = QLabel(f"{key}:")
            label.setFont(QFont(MAIN_FONT_FAMILY, MAIN_FONT_SIZE, QFont.Weight.Bold))
            label.setStyleSheet(f"color: {COLOR_SECONDARY};")

            # Valore
            value_label = QLabel(str(value))

            grid_layout.addWidget(label, row, col * 2)
            grid_layout.addWidget(value_label, row, col * 2 + 1)

            col += 1
            if col >= columns:
                col = 0
                row += 1

        self.content_layout.addWidget(grid_widget)
        return grid_widget

    def add_separator(self):
        """Aggiunge una linea separatrice"""
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet(f"color: {COLOR_BORDER};")
        self.content_layout.addWidget(separator)

    def add_highlight_value(self, label, value, color=None):
        """Aggiunge un valore evidenziato"""
        if color is None:
            color = COLOR_PRIMARY

        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(0, 0, 0, 0)

        # Label
        label_widget = QLabel(f"{label}:")
        label_widget.setFont(QFont(MAIN_FONT_FAMILY, MAIN_FONT_SIZE, QFont.Weight.Bold))
        label_widget.setStyleSheet(f"color: {COLOR_SECONDARY};")

        # Valore evidenziato
        value_widget = QLabel(str(value))
        value_widget.setFont(QFont(MAIN_FONT_FAMILY, MAIN_FONT_SIZE + 2, QFont.Weight.Bold))
        value_widget.setStyleSheet(f"color: {color}; padding: 4px;")

        row_layout.addWidget(label_widget)
        row_layout.addWidget(value_widget)
        row_layout.addStretch()

        self.content_layout.addWidget(row_widget)
        return row_widget

    def add_stats_bars(self, stats_data):
        """Aggiunge barre di statistiche"""
        for stat_name, stat_value in stats_data.items():
            self.add_stat_bar(stat_name, stat_value)

    def add_stat_bar(self, name, value, max_value=20, color=None):
        """Aggiunge una singola barra statistica"""
        if color is None:
            color = COLOR_PRIMARY

        # Assicura che il valore sia nel range corretto
        percentage = min(100, max(0, (value / max_value) * 100))

        bar_widget = QWidget()
        bar_layout = QVBoxLayout(bar_widget)
        bar_layout.setContentsMargins(0, 0, 0, 0)
        bar_layout.setSpacing(2)

        # Nome e valore
        info_layout = QHBoxLayout()
        name_label = QLabel(name)
        name_label.setFont(QFont(MAIN_FONT_FAMILY, MAIN_FONT_SIZE, QFont.Weight.Bold))

        value_label = QLabel(str(value))
        value_label.setStyleSheet(f"color: {color};")

        info_layout.addWidget(name_label)
        info_layout.addStretch()
        info_layout.addWidget(value_label)

        bar_layout.addLayout(info_layout)

        # Barra di progresso
        progress_container = QWidget()
        progress_container.setFixedHeight(8)
        progress_container.setStyleSheet(f"""
            QWidget {{
                background-color: #e9ecef;
                border-radius: 4px;
            }}
        """)

        # Barra riempita
        progress_fill = QWidget(progress_container)
        progress_fill.setFixedHeight(8)
        progress_fill.setFixedWidth(int(progress_container.width() * percentage / 100))
        progress_fill.setStyleSheet(f"""
            QWidget {{
                background-color: {color};
                border-radius: 4px;
            }}
        """)

        bar_layout.addWidget(progress_container)
        self.content_layout.addWidget(bar_widget)

        return bar_widget

    def clear_content(self):
        """Pulisce tutto il contenuto"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def set_data(self, data):
        """Imposta i dati del display"""
        self.info_data = data
        self.refresh_display()

    def refresh_display(self):
        """Aggiorna il display (da implementare nelle sottoclassi)"""
        pass

    def add_custom_widget(self, widget):
        """Aggiunge un widget personalizzato"""
        self.content_layout.addWidget(widget)


class TeamInfoDisplay(InfoDisplay):
    """Display specifico per informazioni squadra"""

    def __init__(self, parent=None):
        super().__init__("Informazioni Squadra", parent)

    def refresh_display(self):
        """Aggiorna il display con i dati della squadra"""
        if not self.info_data:
            return

        self.clear_content()

        # Info base
        self.add_info_row("Nome", self.info_data.get('nome', 'N/A'))
        self.add_info_row("Città", self.info_data.get('citta', 'N/A'))
        self.add_info_row("Stadio", self.info_data.get('stadio', 'N/A'))

        self.add_separator()

        # Info finanziarie evidenziate
        budget = self.info_data.get('budget_trasferimenti_eur', 0)
        self.add_highlight_value("Budget", f"€{budget:,.0f}")

        stipendi = self.info_data.get('budget_stipendi_eur', 0)
        self.add_highlight_value("Stipendi", f"€{stipendi:,.0f}/mese")

        self.add_separator()

        # Statistiche strutture
        facilities = {
            'Reputazione': self.info_data.get('reputazione', 0),
            'Strutture Allenamento': self.info_data.get('strutture_allenamento', 0),
            'Settore Giovanile': self.info_data.get('strutture_giovanili', 0),
            'Reclutamento': self.info_data.get('reclutamento_giovanile', 0)
        }

        self.add_stats_bars(facilities)
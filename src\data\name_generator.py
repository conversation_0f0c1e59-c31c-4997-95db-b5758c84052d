"""
Generatore di nomi per giocatori dalle 110 nazioni disponibili
"""
import random
from typing import Tuple, List, Optional, Dict
from ..core.utils import logger
from .data_loader import data_loader

class NameGenerator:
    """Classe per generazione nomi giocatori da diverse nazioni"""

    def __init__(self):
        self.nations_weights = self._calculate_nations_weights()

    def _calculate_nations_weights(self) -> Dict[str, float]:
        """Calcola pesi per nazioni basati su importanza calcistica"""
        # Pesi maggiori per nazioni con tradizione calcistica
        high_weight_nations = [
            "Italia", "Brasile", "Argentina", "Germania", "Spagna",
            "Francia", "Inghilterra", "Olanda", "Portogallo"
        ]

        medium_weight_nations = [
            "Uruguay", "Belgio", "Croazia", "Colombia", "Messico",
            "Polonia", "Svezia", "Svizzera", "Austria", "Danimarca"
        ]

        weights = {}

        # Assegna pesi basati su importanza
        for nation in data_loader.get_available_nations_for_names():
            if nation in high_weight_nations:
                weights[nation] = 5.0
            elif nation in medium_weight_nations:
                weights[nation] = 3.0
            else:
                weights[nation] = 1.0

        # Peso extra alto per Italia (gioco italiano)
        if "Italia" in weights:
            weights["Italia"] = 8.0

        return weights

    def generate_name_from_nation(self, nation: str) -> Optional[Tuple[str, str]]:
        """Genera nome e cognome da una nazione specifica"""
        names_data = data_loader.get_names_for_nation(nation)

        if not names_data or not names_data.get("first_names") or not names_data.get("last_names"):
            logger.warning(f"Dati nomi non disponibili per {nation}")
            return None

        first_name = random.choice(names_data["first_names"])
        last_name = random.choice(names_data["last_names"])

        return first_name, last_name

    def generate_random_name(self) -> Tuple[str, str, str]:
        """Genera nome casuale da qualsiasi nazione (pesato)"""
        available_nations = data_loader.get_available_nations_for_names()

        if not available_nations:
            logger.error("Nessuna nazione disponibile per generazione nomi - usando fallback italiano")
            return self.generate_italian_name_fallback()

        # Scelta pesata della nazione
        nations = list(self.nations_weights.keys())

        # Filtra solo nazioni con dati disponibili
        valid_nations = [n for n in nations if n in available_nations]

        if not valid_nations:
            logger.warning("Nessuna nazione valida trovata nei pesi - usando tutte le nazioni disponibili")
            valid_nations = available_nations

        weights = [self.nations_weights.get(nation, 1.0) for nation in valid_nations]

        # Normalizza pesi
        total_weight = sum(weights)
        if total_weight == 0:
            logger.warning("Peso totale zero - usando distribuzione uniforme")
            return self.generate_italian_name_fallback()

        weights = [w / total_weight for w in weights]

        chosen_nation = random.choices(valid_nations, weights=weights)[0]

        # Genera nome dalla nazione scelta
        name_result = self.generate_name_from_nation(chosen_nation)

        if name_result:
            first_name, last_name = name_result
            return first_name, last_name, chosen_nation
        else:
            # Fallback su Italia
            logger.warning(f"Fallimento generazione nome per {chosen_nation} - usando fallback italiano")
            return self.generate_italian_name_fallback()

    def generate_italian_name_fallback(self) -> Tuple[str, str, str]:
        """Fallback per nome italiano se altri falliscono"""
        italian_first = [
            "Marco", "Luca", "Andrea", "Alessandro", "Francesco", "Giovanni",
            "Matteo", "Lorenzo", "Davide", "Federico", "Simone", "Gabriele",
            "Riccardo", "Nicola", "Fabio", "Paolo", "Antonio", "Giuseppe"
        ]

        italian_last = [
            "Rossi", "Russo", "Ferrari", "Esposito", "Bianchi", "Romano",
            "Colombo", "Ricci", "Marino", "Greco", "Bruno", "Gallo",
            "Conti", "Costa", "Giordano", "Mancini", "Rizzo", "Lombardi"
        ]

        return random.choice(italian_first), random.choice(italian_last), "Italia"

    def generate_name_for_position(self, position: str) -> Tuple[str, str, str]:
        """Genera nome basato su posizione (alcune nazioni preferite per ruoli)"""
        position_nations = {
            "P": ["Italia", "Germania", "Spagna"],  # Portieri
            "DC": ["Italia", "Brasile", "Germania"],  # Difensori centrali
            "DS": ["Brasile", "Spagna", "Francia"],  # Difensori laterali
            "DD": ["Germania", "Italia", "Olanda"],
            "CC": ["Spagna", "Italia", "Brasile"],  # Centrocampisti
            "CS": ["Brasile", "Francia", "Argentina"],
            "CD": ["Germania", "Olanda", "Inghilterra"],
            "T": ["Argentina", "Brasile", "Spagna"],  # Trequartisti
            "AS": ["Brasile", "Francia", "Portogallo"],  # Attaccanti
            "AD": ["Argentina", "Italia", "Germania"],
            "C": ["Brasile", "Argentina", "Italia"]  # Centravanti
        }

        preferred_nations = position_nations.get(position, ["Italia"])

        # Prova con nazioni preferite per la posizione
        for nation in preferred_nations:
            if nation in data_loader.get_available_nations_for_names():
                name_result = self.generate_name_from_nation(nation)
                if name_result:
                    return name_result[0], name_result[1], nation

        # Se fallisce, nome casuale
        return self.generate_random_name()

    def generate_youth_name(self) -> Tuple[str, str, str]:
        """Genera nome per giovane (preferenza Italia e nazioni vicine)"""
        youth_preferred_nations = [
            "Italia", "Francia", "Spagna", "Germania",
            "Svizzera", "Austria", "Croazia", "Albania"
        ]

        available_preferred = [
            nation for nation in youth_preferred_nations
            if nation in data_loader.get_available_nations_for_names()
        ]

        if available_preferred:
            chosen_nation = random.choice(available_preferred)
            name_result = self.generate_name_from_nation(chosen_nation)
            if name_result:
                return name_result[0], name_result[1], chosen_nation

        return self.generate_random_name()

    def generate_multiple_names(self, count: int, nation: Optional[str] = None) -> List[Tuple[str, str, str]]:
        """Genera multipli nomi"""
        names = []
        used_combinations = set()

        for _ in range(count):
            attempts = 0
            max_attempts = 100

            while attempts < max_attempts:
                if nation:
                    name_result = self.generate_name_from_nation(nation)
                    if name_result:
                        first_name, last_name = name_result
                        full_name = (first_name, last_name, nation)
                    else:
                        full_name = self.generate_random_name()
                else:
                    full_name = self.generate_random_name()

                # Evita duplicati
                name_key = (full_name[0], full_name[1])
                if name_key not in used_combinations:
                    used_combinations.add(name_key)
                    names.append(full_name)
                    break

                attempts += 1

            # Se non riesce a trovare un nome unico, aggiunge comunque
            if attempts >= max_attempts:
                names.append(self.generate_random_name())

        return names

    def get_nation_player_style(self, nation: str) -> Dict[str, float]:
        """Restituisce caratteristiche tipiche per nazione (per generazione attributi)"""
        nation_styles = {
            "Italia": {"tecnica": 1.2, "difesa": 1.3, "mentalita": 1.1},
            "Brasile": {"tecnica": 1.4, "dribbling": 1.3, "velocita": 1.2},
            "Argentina": {"tecnica": 1.3, "dribbling": 1.2, "tiro": 1.1},
            "Germania": {"forza": 1.2, "mentalita": 1.3, "resistenza": 1.2},
            "Spagna": {"tecnica": 1.3, "passaggio": 1.4, "dribbling": 1.1},
            "Francia": {"velocita": 1.2, "forza": 1.1, "tecnica": 1.2},
            "Inghilterra": {"forza": 1.2, "resistenza": 1.3, "mentalita": 1.1},
            "Olanda": {"tecnica": 1.2, "passaggio": 1.2, "mentalita": 1.1},
            "Portogallo": {"tecnica": 1.2, "dribbling": 1.2, "velocita": 1.1},
        }

        return nation_styles.get(nation, {})

    def get_nations_statistics(self) -> Dict[str, int]:
        """Restituisce statistiche sulle nazioni disponibili"""
        stats = {}
        for nation in data_loader.get_available_nations_for_names():
            names_data = data_loader.get_names_for_nation(nation)
            first_names_count = len(names_data.get("first_names", []))
            last_names_count = len(names_data.get("last_names", []))
            stats[nation] = {
                "first_names": first_names_count,
                "last_names": last_names_count,
                "total_combinations": first_names_count * last_names_count,
                "weight": self.nations_weights.get(nation, 1.0)
            }
        return stats

    def validate_names_availability(self) -> bool:
        """Valida disponibilità nomi per generazione"""
        available_nations = data_loader.get_available_nations_for_names()

        if not available_nations:
            logger.error("Nessuna nazione disponibile")
            return False

        valid_nations = 0
        for nation in available_nations:
            names_data = data_loader.get_names_for_nation(nation)
            if (names_data.get("first_names") and names_data.get("last_names") and
                len(names_data["first_names"]) > 0 and len(names_data["last_names"]) > 0):
                valid_nations += 1

        logger.info(f"{valid_nations}/{len(available_nations)} nazioni hanno dati validi")
        return valid_nations > 0

# Istanza globale del generatore di nomi
name_generator = NameGenerator()
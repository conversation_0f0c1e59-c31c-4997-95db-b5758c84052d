"""
Sistema di generazione squadre basato su reputazione e caratteristiche club
"""
import random
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .player_generator import PlayerGenerator, player_generator
from .player import Player, PlayerPosition
from ..data.data_loader import data_loader
from ..competitions.coefficient_system import uefa_coefficient_system
from ..core.utils import logger, clamp

class TeamTier(Enum):
    """Livelli di squadra basati su reputazione"""
    ELITE = "Elite"           # Reputazione 18-20 (Bayern, Real Madrid, etc.)
    TOP = "Top"               # Reputazione 15-17 (Atalanta, Valencia, etc.)  
    GOOD = "Good"             # Reputazione 12-14 (Mid-table teams)
    AVERAGE = "Average"       # Reputazione 9-11 (Lower table teams)
    WEAK = "Weak"             # Reputazione 6-8 (Relegation candidates)
    POOR = "Poor"             # Reputazione 1-5 (Very weak teams)

@dataclass
class TeamSquadProfile:
    """Profilo per generazione squadra"""
    team_name: str
    country: str
    league_name: str
    reputation: int
    tier: TeamTier
    
    # Caratteristiche tecniche
    avg_overall_range: Tuple[int, int]
    star_player_count: int
    youth_player_count: int
    
    # Nazionalità
    nationality_preference: str
    national_player_ratio: float  # % di giocatori nazionali
    
    # Budget e strutture
    budget_millions: int
    youth_facilities: int
    training_facilities: int

class TeamSquadGenerator:
    """Generatore COMPLETO di rose squadra - SISTEMA AUTONOMO che usa tutte le 110 nazioni"""
    
    def __init__(self):
        # Carica tutte le 110 nazioni disponibili
        self.all_nations = self._load_all_nations()
        logger.info(f"TeamSquadGenerator inizializzato con {len(self.all_nations)} nazioni")
        
        # Coefficienti UEFA per determinare qualità nazionale
        self.uefa_coefficients = self._load_uefa_coefficients()
        
        # Configurazioni tier
        self.tier_configs = self._setup_tier_configurations()
        
        # Cache per profili squadra
        self._team_profiles_cache = {}
    
    def _load_all_nations(self) -> List[str]:
        """Carica TUTTE le 110 nazioni disponibili dal data_loader"""
        if not data_loader.is_loaded():
            data_loader.load_all_data()
        
        # Ottieni tutte le nazioni con nomi disponibili
        nation_names = data_loader.get_all_nation_names()
        if nation_names:
            logger.info(f"Caricate {len(nation_names)} nazioni per generazione giocatori")
            return list(nation_names)
        
        # Fallback alle nazioni principali se non disponibili
        fallback_nations = [
            "Italia", "Brasile", "Argentina", "Francia", "Spagna", "Germania", 
            "Inghilterra", "Portogallo", "Olanda", "Uruguay", "Colombia", "Belgio",
            "Croazia", "Danimarca", "Svezia", "Norvegia", "Polonia", "Repubblica Ceca",
            "Ungheria", "Austria", "Svizzera", "Grecia", "Serbia", "Turchia",
            "Russia", "Ucraina", "Romania", "Bulgaria", "Slovacchia", "Slovenia"
        ]
        logger.warning(f"Usando nazioni fallback: {len(fallback_nations)} nazioni")
        return fallback_nations
    
    def _load_uefa_coefficients(self) -> Dict[str, float]:
        """Carica coefficienti UEFA per determinare forza nazioni"""
        # Coefficienti basati su ranking FIFA/UEFA (semplificato)
        coefficients = {
            # Top tier (Elite)
            "Brasile": 95, "Argentina": 94, "Francia": 93, "Belgio": 91, "Inghilterra": 90,
            "Italia": 89, "Spagna": 88, "Portogallo": 87, "Olanda": 86, "Germania": 85,
            
            # High tier (Strong)
            "Croazia": 82, "Danimarca": 80, "Uruguay": 78, "Svizzera": 76, "Colombia": 75,
            "Messico": 74, "Svezia": 72, "Polonia": 70, "Austria": 68, "Ucraina": 66,
            
            # Mid tier (Good)
            "Serbia": 64, "Marocco": 62, "Giappone": 60, "Iran": 58, "Turchia": 56,
            "Repubblica Ceca": 54, "Norvegia": 52, "Ungheria": 50, "Russia": 48, "Perù": 46,
            
            # Lower tiers con gradazione
            "Romania": 44, "Slovacchia": 42, "Irlanda": 40, "Bulgaria": 38, "Grecia": 36,
            "Slovenia": 34, "Finlandia": 32, "Bosnia": 30, "Montenegro": 28, "Albania": 26
        }
        
        # Assegna coefficienti a tutte le altre nazioni non specificate
        for nation in self.all_nations:
            if nation not in coefficients:
                # Distribuzione graduale per le nazioni rimanenti
                remaining_nations = [n for n in self.all_nations if n not in coefficients]
                base_coeff = 25
                variation = len(remaining_nations)
                if nation in remaining_nations:
                    nation_index = remaining_nations.index(nation)
                    coefficients[nation] = max(5, base_coeff - (nation_index * 20 // max(1, variation)))
                else:
                    coefficients[nation] = 15  # Default medio
        
        logger.info(f"Coefficienti caricati per {len(coefficients)} nazioni")
        return coefficients
    
    def _setup_tier_configurations(self) -> Dict[TeamTier, Dict]:
        """Configurazioni per ogni tier di squadra"""
        return {
            TeamTier.ELITE: {
                "overall_base": (16, 19),
                "star_players": 3,
                "youth_players": 2,
                "national_ratio": 0.35,  # 35% nazionali per max internazionalità
                "foreign_star_bonus": 2
            },
            TeamTier.TOP: {
                "overall_base": (14, 17),
                "star_players": 2,
                "youth_players": 3,
                "national_ratio": 0.45,
                "foreign_star_bonus": 1
            },
            TeamTier.GOOD: {
                "overall_base": (12, 15),
                "star_players": 1,
                "youth_players": 4,
                "national_ratio": 0.55,
                "foreign_star_bonus": 1
            },
            TeamTier.AVERAGE: {
                "overall_base": (10, 14),
                "star_players": 1,
                "youth_players": 5,
                "national_ratio": 0.70,
                "foreign_star_bonus": 0
            },
            TeamTier.WEAK: {
                "overall_base": (8, 12),
                "star_players": 0,
                "youth_players": 6,
                "national_ratio": 0.80,
                "foreign_star_bonus": 0
            },
            TeamTier.POOR: {
                "overall_base": (6, 10),
                "star_players": 0,
                "youth_players": 8,
                "national_ratio": 0.90,
                "foreign_star_bonus": 0
            }
        }
    
    def analyze_team(self, team_data: Dict, country: str, league_name: str) -> TeamSquadProfile:
        """Analizza una squadra e crea il profilo per generazione"""
        
        reputation = team_data.get("reputazione", 10)
        team_name = team_data.get("nome", "Unknown Team")
        
        # Determina tier basato su reputazione
        tier = self._determine_team_tier(reputation)
        
        # Calcola range overall basato su tier e reputazione specifica
        config = self.tier_configs[tier]
        base_min, base_max = config["overall_base"]
        
        # Aggiusta range basato su reputazione specifica
        reputation_modifier = (reputation - 10) * 0.3  # ±3 punti massimo
        avg_min = clamp(int(base_min + reputation_modifier), 5, 18)
        avg_max = clamp(int(base_max + reputation_modifier), 7, 20)
        
        # Determina nazionalità preferita e ratio
        nationality_preference = country
        national_ratio = config["national_ratio"]
        
        # Aggiusta ratio basato su coefficiente paese
        country_coeff = uefa_coefficient_system.get_country_coefficient(country)
        if country_coeff:
            if country_coeff.ranking <= 6:  # Top 6 leghe europee
                national_ratio *= 0.8  # Più stranieri nelle top leghe
            elif country_coeff.ranking >= 20:  # Leghe minori
                national_ratio = min(0.95, national_ratio * 1.1)  # Più nazionali
        
        # Calcola budget approssimativo (da migliaia a milioni)
        budget_eur = team_data.get("budget_trasferimenti_eur", 5000000)
        budget_millions = max(1, budget_eur // 1000000)
        
        return TeamSquadProfile(
            team_name=team_name,
            country=country,
            league_name=league_name,
            reputation=reputation,
            tier=tier,
            avg_overall_range=(avg_min, avg_max),
            star_player_count=config["star_players"],
            youth_player_count=config["youth_players"],
            nationality_preference=nationality_preference,
            national_player_ratio=national_ratio,
            budget_millions=budget_millions,
            youth_facilities=team_data.get("strutture_giovanili", 5),
            training_facilities=team_data.get("strutture_allenamento", 5)
        )
    
    def _determine_team_tier(self, reputation: int) -> TeamTier:
        """Determina il tier di una squadra basato sulla reputazione"""
        if reputation >= 18:
            return TeamTier.ELITE
        elif reputation >= 15:
            return TeamTier.TOP
        elif reputation >= 12:
            return TeamTier.GOOD
        elif reputation >= 9:
            return TeamTier.AVERAGE
        elif reputation >= 6:
            return TeamTier.WEAK
        else:
            return TeamTier.POOR
    
    def generate_team_squad(self, team_profile: TeamSquadProfile, 
                          squad_size: int = 25) -> List[Player]:
        """Genera rosa completa per una squadra basata sul profilo"""
        
        logger.info(f"Generando rosa per {team_profile.team_name} ({team_profile.tier.value}, rep: {team_profile.reputation})")
        
        squad = []
        config = self.tier_configs[team_profile.tier]
        
        # 1. Genera giocatori normali (maggioranza della rosa)
        normal_player_count = squad_size - team_profile.star_player_count - team_profile.youth_player_count
        normal_players = self._generate_normal_players(
            team_profile, normal_player_count
        )
        squad.extend(normal_players)
        
        # 2. Genera star players (se previsti)
        if team_profile.star_player_count > 0:
            star_players = self._generate_star_players(
                team_profile, team_profile.star_player_count
            )
            squad.extend(star_players)
        
        # 3. Genera giovani promesse
        youth_players = self._generate_youth_players(
            team_profile, team_profile.youth_player_count
        )
        squad.extend(youth_players)
        
        # 4. Assegna numeri di maglia
        self._assign_shirt_numbers(squad)
        
        # 5. Log statistiche finali
        self._log_squad_statistics(squad, team_profile)
        
        return squad
    
    def _generate_normal_players(self, profile: TeamSquadProfile, count: int) -> List[Player]:
        """Genera giocatori normali per la squadra"""
        players = []
        
        for _ in range(count):
            # Determina se sarà nazionale o straniero
            is_national = random.random() < profile.national_player_ratio
            nationality = profile.nationality_preference if is_national else None
            
            # Età tipica per giocatori normali (22-32)
            age_range = (22, 32)
            
            # Overall nel range normale della squadra
            overall_range = profile.avg_overall_range
            
            player = self.player_gen.generate_player(
                overall_rating_range=overall_range,
                age_range=age_range,
                nationality=nationality
            )
            
            players.append(player)
        
        return players
    
    def _generate_star_players(self, profile: TeamSquadProfile, count: int) -> List[Player]:
        """Genera star players per la squadra"""
        players = []
        config = self.tier_configs[profile.tier]
        
        for _ in range(count):
            # Star players hanno overall più alto
            star_min = max(profile.avg_overall_range[1] - 1, profile.avg_overall_range[0])
            star_max = min(20, profile.avg_overall_range[1] + 2)
            
            # Età prime per star players (24-30)
            age_range = (24, 30)
            
            # Possibilità di essere straniero per appeal internazionale
            foreign_star_chance = 0.7 if profile.tier in [TeamTier.ELITE, TeamTier.TOP] else 0.3
            is_foreign_star = random.random() < foreign_star_chance
            
            if is_foreign_star:
                # Stelle straniere da nazioni forti
                star_nations = ["Brasile", "Argentina", "Francia", "Portogallo", "Spagna"]
                nationality = random.choice(star_nations)
                # Bonus overall per stelle straniere
                star_max = min(20, star_max + config.get("foreign_star_bonus", 0))
            else:
                nationality = profile.nationality_preference
            
            player = self.player_gen.generate_player(
                overall_rating_range=(star_min, star_max),
                age_range=age_range,
                nationality=nationality
            )
            
            players.append(player)
        
        return players
    
    def _generate_youth_players(self, profile: TeamSquadProfile, count: int) -> List[Player]:
        """Genera giovani giocatori per la squadra"""
        players = []
        
        for _ in range(count):
            # Giovani sono prevalentemente nazionali
            youth_national_ratio = min(0.9, profile.national_player_ratio + 0.2)
            is_national = random.random() < youth_national_ratio
            nationality = profile.nationality_preference if is_national else None
            
            # Età giovane
            age_range = (16, 21)
            
            # Overall più basso ma con potenziale
            youth_min = max(6, profile.avg_overall_range[0] - 4)
            youth_max = profile.avg_overall_range[1] - 2
            
            # Bonus per strutture giovanili di qualità
            if profile.youth_facilities >= 8:
                youth_max += 1
            
            player = self.player_gen.generate_player(
                overall_rating_range=(youth_min, youth_max),
                age_range=age_range,
                nationality=nationality
            )
            
            players.append(player)
        
        return players
    
    def _assign_shirt_numbers(self, squad: List[Player]):
        """Assegna numeri di maglia alla rosa"""
        available_numbers = list(range(1, 100))
        random.shuffle(available_numbers)
        
        # Portieri hanno numeri bassi preferibilmente
        goalkeepers = [p for p in squad if p.position == PlayerPosition.PORTIERE]
        for i, gk in enumerate(goalkeepers):
            gk.shirt_number = i + 1
        
        # Altri giocatori
        other_players = [p for p in squad if p.position != PlayerPosition.PORTIERE]
        number_index = len(goalkeepers)
        
        for player in other_players:
            player.shirt_number = available_numbers[number_index]
            number_index += 1
    
    def _log_squad_statistics(self, squad: List[Player], profile: TeamSquadProfile):
        """Log statistiche della rosa generata"""
        national_players = len([p for p in squad if p.nationality == profile.nationality_preference])
        avg_overall = sum(p.overall_rating for p in squad) / len(squad)
        avg_age = sum(p.age for p in squad) / len(squad)
        
        positions = {}
        for player in squad:
            pos = player.position.value
            positions[pos] = positions.get(pos, 0) + 1
        
        logger.info(f"{profile.team_name}: {len(squad)} giocatori generati")
        logger.info(f"  Overall medio: {avg_overall:.1f}, Età media: {avg_age:.1f}")
        logger.info(f"  Nazionali: {national_players}/{len(squad)} ({national_players/len(squad)*100:.1f}%)")
        logger.info(f"  Posizioni: {dict(sorted(positions.items()))}")
    
    def generate_all_european_squads(self, squad_size: int = 25) -> Dict[str, Dict[str, List[Player]]]:
        """Genera rose per tutte le squadre europee caricate"""
        
        logger.info("=== GENERAZIONE SQUADRE EUROPEE ===")
        all_squads = {}
        
        # Carica tutti i dati se non già fatto
        if not data_loader.is_loaded():
            data_loader.load_all_data()
        
        # Genera per tutte le nazioni
        for country, leagues in data_loader.leagues_data.items():
            country_squads = {}
            
            logger.info(f"Generando squadre {country}...")
            
            for league_name, league_data in leagues.items():
                teams = league_data.get("squadre", [])
                
                for team_data in teams:
                    # Analizza squadra
                    profile = self.analyze_team(team_data, country, league_name)
                    
                    # Genera rosa
                    squad = self.generate_team_squad(profile, squad_size)
                    
                    # Salva rosa
                    if country not in all_squads:
                        all_squads[country] = {}
                    if league_name not in all_squads[country]:
                        all_squads[country][league_name] = {}
                    
                    all_squads[country][league_name][team_data["nome"]] = squad
            
            logger.info(f"Completato {country}: {sum(len(leagues) for leagues in country_squads.values())} squadre")
        
        # Statistiche finali
        total_teams = sum(
            len(teams) for country in all_squads.values() 
            for league in country.values() 
            for teams in league.values()
        )
        total_players = total_teams * squad_size
        
        logger.info(f"Generazione completata: {total_teams} squadre, ~{total_players} giocatori")
        
        return all_squads

# Istanza globale del generatore squadre
team_squad_generator = TeamSquadGenerator()
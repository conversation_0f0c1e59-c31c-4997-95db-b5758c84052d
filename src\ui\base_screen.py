"""
Classe base per tutte le schermate dell'applicazione
Fornisce funzionalità comuni e struttura standardizzata
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from core.config import (COLOR_BACKGROUND, COLOR_SURFACE, COLOR_TEXT,
                          HEADER_FONT_SIZE, MAIN_FONT_FAMILY)


class BaseScreen(QWidget):
    """Classe base per tutte le schermate dell'applicazione"""

    # Segnali comuni
    data_changed = pyqtSignal(dict)
    screen_ready = pyqtSignal()
    back_requested = pyqtSignal()

    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.screen_title = title
        self.screen_data = {}
        self.is_initialized = False
        self.setup_base_ui()

    def setup_base_ui(self):
        """Configura l'interfaccia base della schermata"""
        # Layout principale
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(20)

        # Header della schermata
        if self.screen_title:
            self.create_header()

        # Contenitore per il contenuto specifico
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(15)

        self.main_layout.addWidget(self.content_widget)

        # Stile base
        self.setStyleSheet(f"""
            BaseScreen {{
                background-color: {COLOR_BACKGROUND};
                color: {COLOR_TEXT};
            }}
            QWidget {{
                background-color: {COLOR_BACKGROUND};
                color: {COLOR_TEXT};
            }}
        """)

    def create_header(self):
        """Crea l'header della schermata con titolo"""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 10)

        # Titolo
        title_label = QLabel(self.screen_title)
        title_font = QFont(MAIN_FONT_FAMILY, HEADER_FONT_SIZE, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {COLOR_TEXT};
                padding: 10px 0px;
                border-bottom: 2px solid #dee2e6;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        self.main_layout.addWidget(header_widget)

    def initialize(self):
        """Inizializza la schermata (chiamato quando viene mostrata)"""
        if not self.is_initialized:
            self.setup_content()
            self.setup_connections()
            self.is_initialized = True

        self.refresh()
        self.screen_ready.emit()

    def setup_content(self):
        """Configura il contenuto specifico della schermata (da implementare)"""
        pass

    def setup_connections(self):
        """Configura le connessioni dei segnali (da implementare)"""
        pass

    def refresh(self):
        """Aggiorna il contenuto della schermata (da implementare)"""
        pass

    def set_data(self, data):
        """Imposta i dati per la schermata"""
        self.screen_data.update(data)
        if self.is_initialized:
            self.refresh()

    def get_data(self):
        """Restituisce i dati della schermata"""
        return self.screen_data.copy()

    def add_content_widget(self, widget):
        """Aggiunge un widget al contenuto della schermata"""
        self.content_layout.addWidget(widget)

    def add_content_layout(self, layout):
        """Aggiunge un layout al contenuto della schermata"""
        self.content_layout.addLayout(layout)

    def clear_content(self):
        """Pulisce il contenuto della schermata"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def show_loading(self, message="Caricamento..."):
        """Mostra un indicatore di caricamento"""
        loading_label = QLabel(message)
        loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        loading_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                padding: 40px;
            }
        """)
        self.clear_content()
        self.add_content_widget(loading_label)

    def hide_loading(self):
        """Nasconde l'indicatore di caricamento"""
        self.clear_content()

    def cleanup(self):
        """Pulisce le risorse della schermata"""
        self.clear_content()
        self.screen_data.clear()
        self.is_initialized = False
"""
Pannello gestione contratti giocatori
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QGroupBox, QTabWidget, QPushButton, QSpinBox,
                            QComboBox, QProgressBar)
from PyQt6.QtCore import Qt, pyqtSignal

from ..components.data_table import DataTable
from ..components.info_display import InfoDisplay
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR,
                           MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class ContractsPanel(QWidget):
    """Pannello per gestione contratti"""

    contract_renewed = pyqtSignal(dict)
    negotiation_started = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.contracts_data = []
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        self.create_panel_header(main_layout)

        # Tab widget principale
        self.tab_widget = QTabWidget()

        # Tab Contratti Attuali
        self.create_current_contracts_tab()

        # Tab Rinnovi
        self.create_renewals_tab()

        # Tab Scadenze
        self.create_expiring_tab()

        main_layout.addWidget(self.tab_widget)

    def create_panel_header(self, parent_layout):
        """Crea l'header del pannello"""
        header_layout = QHBoxLayout()

        title_label = QLabel("Gestione Contratti")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Budget stipendi
        self.salary_budget_label = QLabel("Budget Stipendi: €0/mese")
        self.salary_budget_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                color: {COLOR_SUCCESS};
                padding: 5px 10px;
                border: 1px solid {COLOR_SUCCESS};
                border-radius: 4px;
            }}
        """)
        header_layout.addWidget(self.salary_budget_label)

        parent_layout.addLayout(header_layout)

    def create_current_contracts_tab(self):
        """Crea la tab contratti attuali"""
        contracts_widget = QWidget()
        layout = QVBoxLayout(contracts_widget)

        # Tabella contratti
        columns = [
            {"key": "giocatore", "title": "Giocatore", "width": 150, "stretch": True},
            {"key": "posizione", "title": "Ruolo", "width": 60, "align": "center"},
            {"key": "stipendio", "title": "Stipendio", "width": 100, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}/mese" if isinstance(x, (int, float)) else "N/A"},
            {"key": "scadenza", "title": "Scadenza", "width": 100, "align": "center"},
            {"key": "clausola", "title": "Clausola", "width": 100, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}" if isinstance(x, (int, float)) else "Nessuna"},
            {"key": "stato", "title": "Stato", "width": 100, "align": "center"}
        ]

        self.contracts_table = DataTable(columns)
        self.contracts_table.row_selected.connect(self.on_contract_selected)
        layout.addWidget(self.contracts_table)

        # Azioni contratto
        actions_layout = QHBoxLayout()
        
        self.renew_btn = QPushButton("Rinnova Contratto")
        self.renew_btn.setEnabled(False)
        self.renew_btn.clicked.connect(self.start_renewal)
        actions_layout.addWidget(self.renew_btn)

        self.modify_btn = QPushButton("Modifica Clausola")
        self.modify_btn.setEnabled(False)
        actions_layout.addWidget(self.modify_btn)

        actions_layout.addStretch()
        layout.addLayout(actions_layout)

        # Carica dati di esempio
        self.load_sample_contracts()

        self.tab_widget.addTab(contracts_widget, "📋 Contratti")

    def create_renewals_tab(self):
        """Crea la tab rinnovi"""
        renewals_widget = QWidget()
        layout = QVBoxLayout(renewals_widget)

        # Trattative rinnovi
        renewals_group = QGroupBox("Trattative di Rinnovo")
        renewals_layout = QVBoxLayout(renewals_group)

        self.renewals_table = DataTable([
            {"key": "giocatore", "title": "Giocatore", "width": 150, "stretch": True},
            {"key": "stipendio_attuale", "title": "Stipendio Attuale", "width": 120, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}/mese" if isinstance(x, (int, float)) else "N/A"},
            {"key": "richiesta", "title": "Richiesta", "width": 120, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}/mese" if isinstance(x, (int, float)) else "N/A"},
            {"key": "stato_trattativa", "title": "Stato", "width": 100, "align": "center"},
            {"key": "scadenza_offerta", "title": "Scadenza", "width": 100, "align": "center"}
        ])
        renewals_layout.addWidget(self.renewals_table)

        layout.addWidget(renewals_group)

        # Dettagli rinnovo
        details_group = QGroupBox("Dettagli Rinnovo")
        details_layout = QVBoxLayout(details_group)

        self.renewal_details = InfoDisplay()
        details_layout.addWidget(self.renewal_details)

        layout.addWidget(details_group)

        self.tab_widget.addTab(renewals_widget, "🔄 Rinnovi")

    def create_expiring_tab(self):
        """Crea la tab scadenze"""
        expiring_widget = QWidget()
        layout = QVBoxLayout(expiring_widget)

        # Contratti in scadenza
        expiring_group = QGroupBox("Contratti in Scadenza")
        expiring_layout = QVBoxLayout(expiring_group)

        # Filtro scadenze
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("Mostra scadenze entro:"))
        
        self.expiry_filter = QComboBox()
        self.expiry_filter.addItems([
            "6 mesi", "1 anno", "2 anni", "Tutti"
        ])
        self.expiry_filter.currentTextChanged.connect(self.filter_expiring)
        filter_layout.addWidget(self.expiry_filter)
        
        filter_layout.addStretch()
        expiring_layout.addLayout(filter_layout)

        # Tabella scadenze
        self.expiring_table = DataTable([
            {"key": "giocatore", "title": "Giocatore", "width": 150, "stretch": True},
            {"key": "posizione", "title": "Ruolo", "width": 60, "align": "center"},
            {"key": "scadenza", "title": "Scadenza", "width": 100, "align": "center"},
            {"key": "giorni_rimasti", "title": "Giorni", "width": 80, "align": "center"},
            {"key": "priorita", "title": "Priorità", "width": 100, "align": "center"},
            {"key": "azione", "title": "Azione", "width": 120, "align": "center"}
        ])
        expiring_layout.addWidget(self.expiring_table)

        layout.addWidget(expiring_group)

        # Azioni prioritarie
        priority_group = QGroupBox("Azioni Prioritarie")
        priority_layout = QVBoxLayout(priority_group)

        priority_actions = QHBoxLayout()
        
        self.renew_all_btn = QPushButton("Rinnova Tutti Prioritari")
        priority_actions.addWidget(self.renew_all_btn)

        self.release_btn = QPushButton("Rilascia Selezionati")
        priority_actions.addWidget(self.release_btn)

        priority_actions.addStretch()
        priority_layout.addLayout(priority_actions)

        layout.addWidget(priority_group)

        self.tab_widget.addTab(expiring_widget, "⏰ Scadenze")

    def load_sample_contracts(self):
        """Carica contratti di esempio"""
        sample_contracts = [
            {
                "giocatore": "Dusan Vlahovic", "posizione": "C",
                "stipendio": 7000000, "scadenza": "2028-06-30",
                "clausola": 80000000, "stato": "Attivo"
            },
            {
                "giocatore": "Federico Chiesa", "posizione": "E",
                "stipendio": 5000000, "scadenza": "2025-06-30",
                "clausola": 60000000, "stato": "In Scadenza"
            },
            {
                "giocatore": "Manuel Locatelli", "posizione": "CC",
                "stipendio": 3500000, "scadenza": "2027-06-30",
                "clausola": 0, "stato": "Attivo"
            },
            {
                "giocatore": "Wojciech Szczesny", "posizione": "P",
                "stipendio": 6500000, "scadenza": "2025-06-30",
                "clausola": 0, "stato": "In Scadenza"
            }
        ]
        
        self.contracts_data = sample_contracts
        self.contracts_table.set_data(sample_contracts)

        # Filtra contratti in scadenza
        expiring_contracts = [c for c in sample_contracts if c['stato'] == 'In Scadenza']
        for contract in expiring_contracts:
            contract['giorni_rimasti'] = 180  # Esempio
            contract['priorita'] = 'Alta'
            contract['azione'] = 'Rinnova'
        
        self.expiring_table.set_data(expiring_contracts)

    def on_contract_selected(self, row_index, contract_data):
        """Gestisce la selezione di un contratto"""
        self.selected_contract = contract_data
        self.renew_btn.setEnabled(True)
        self.modify_btn.setEnabled(True)

    def start_renewal(self):
        """Avvia il rinnovo di un contratto"""
        if hasattr(self, 'selected_contract'):
            renewal_data = {
                'player': self.selected_contract['giocatore'],
                'current_salary': self.selected_contract['stipendio'],
                'proposed_salary': self.selected_contract['stipendio'] * 1.2,  # +20%
                'duration': 3  # 3 anni
            }
            self.negotiation_started.emit(renewal_data)

    def filter_expiring(self):
        """Filtra i contratti in scadenza"""
        # Implementazione filtro scadenze
        pass

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data
        club_data = game_data.get('selected_club', {})
        
        # Aggiorna budget stipendi
        salary_budget = club_data.get('budget_stipendi_eur', 0)
        self.salary_budget_label.setText(f"Budget Stipendi: €{salary_budget:,.0f}/mese")

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'contracts',
            'contracts_count': len(self.contracts_data),
            'expiring_count': len([c for c in self.contracts_data if c['stato'] == 'In Scadenza'])
        }

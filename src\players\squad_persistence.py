"""
Sistema di persistenza per rose squadre generate
"""
import json
from typing import List, Dict, Optional
from pathlib import Path
from datetime import datetime

from .player import Player
from ..core.config import BASE_DIR
from ..core.utils import logger, save_json_file, load_json_file

class SquadPersistenceManager:
    """Gestore salvataggio e caricamento rose squadre"""
    
    def __init__(self):
        self.squads_dir = BASE_DIR / "data" / "generated_squads"
        self.squads_dir.mkdir(parents=True, exist_ok=True)
        
    def save_all_squads(self, squads_data: Dict[str, Dict[str, List[Player]]], 
                       filename: Optional[str] = None) -> bool:
        """Salva tutte le rose generate in un file JSON"""
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"european_squads_{timestamp}.json"
        
        filepath = self.squads_dir / filename
        
        try:
            # Converti i giocatori in formato serializzabile
            serializable_data = {}
            
            for country, leagues in squads_data.items():
                serializable_data[country] = {}
                
                for league_name, teams in leagues.items():
                    serializable_data[country][league_name] = {}
                    
                    for team_name, players in teams.items():
                        serializable_data[country][league_name][team_name] = [
                            self._player_to_dict(player) for player in players
                        ]
            
            # Aggiungi metadati
            final_data = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "total_countries": len(serializable_data),
                    "total_teams": sum(
                        len(teams) for leagues in serializable_data.values() 
                        for teams in leagues.values()
                    ),
                    "total_players": sum(
                        len(players) for leagues in serializable_data.values() 
                        for teams in leagues.values() 
                        for players in teams.values()
                    )
                },
                "squads": serializable_data
            }
            
            # Salva file
            success = save_json_file(final_data, filepath)
            
            if success:
                logger.info(f"Rose salvate in: {filepath}")
                logger.info(f"Totale: {final_data['metadata']['total_teams']} squadre, {final_data['metadata']['total_players']} giocatori")
            
            return success
            
        except Exception as e:
            logger.error(f"Errore salvataggio rose: {e}")
            return False
    
    def load_squads(self, filename: str) -> Optional[Dict[str, Dict[str, List[Player]]]]:
        """Carica rose da file JSON"""
        
        filepath = self.squads_dir / filename
        
        try:
            data = load_json_file(filepath)
            if not data:
                return None
            
            squads_data = data.get("squads", {})
            metadata = data.get("metadata", {})
            
            logger.info(f"Caricando rose da: {filepath}")
            logger.info(f"Generate il: {metadata.get('generated_at', 'N/A')}")
            
            # Converti i dizionari giocatori in oggetti Player
            converted_squads = {}
            
            for country, leagues in squads_data.items():
                converted_squads[country] = {}
                
                for league_name, teams in leagues.items():
                    converted_squads[country][league_name] = {}
                    
                    for team_name, players_data in teams.items():
                        converted_squads[country][league_name][team_name] = [
                            self._dict_to_player(player_dict) for player_dict in players_data
                        ]
            
            logger.info(f"Rose caricate: {metadata.get('total_teams', 0)} squadre, {metadata.get('total_players', 0)} giocatori")
            return converted_squads
            
        except Exception as e:
            logger.error(f"Errore caricamento rose: {e}")
            return None
    
    def _player_to_dict(self, player: Player) -> Dict:
        """Converte oggetto Player in dizionario serializzabile"""
        from ..core.config import PLAYER_ATTRIBUTES
        
        return {
            "id": player.id,
            "first_name": player.first_name,
            "last_name": player.last_name,
            "nationality": player.nationality,
            "birth_date": player.birth_date.isoformat(),
            "position": player.position.value,
            "secondary_positions": [pos.value for pos in player.secondary_positions],
            "foot_preference": player.foot_preference.value,
            "shirt_number": player.shirt_number,
            
            # Attributi
            "attributes": {
                attr: getattr(player.attributes, attr) 
                for attr in PLAYER_ATTRIBUTES
            },
            "potential": player.potential,
            
            # Status
            "injury_status": player.injury_status.value,
            "injury_days_left": player.injury_days_left,
            "morale": player.morale,
            "fitness": player.fitness,
            
            # Statistiche
            "current_season_stats": {
                "presenze": player.current_season_stats.presenze,
                "minuti_giocati": player.current_season_stats.minuti_giocati,
                "gol": player.current_season_stats.gol,
                "assist": player.current_season_stats.assist,
                "cartellini_gialli": player.current_season_stats.cartellini_gialli,
                "cartellini_rossi": player.current_season_stats.cartellini_rossi,
                "voto_medio": player.current_season_stats.voto_medio
            },
            
            # Contratto
            "contract": {
                "stipendio_annuale": player.contract.stipendio_annuale,
                "data_scadenza": player.contract.data_scadenza.isoformat(),
                "clausola_rescissoria": player.contract.clausola_rescissoria,
                "status": player.contract.status.value
            },
            
            # Club
            "current_club": player.current_club,
            "is_on_loan": player.is_on_loan,
            "loan_club": player.loan_club,
            
            # Calcolati
            "overall_rating": player.overall_rating,
            "market_value": player.market_value,
            "age": player.age
        }
    
    def _dict_to_player(self, player_dict: Dict) -> Player:
        """Converte dizionario in oggetto Player"""
        from datetime import date
        from .player import PlayerPosition, FootPreference, InjuryStatus, ContractStatus, PlayerStats, PlayerContract, PlayerAttributes
        
        # Crea player base
        birth_date = date.fromisoformat(player_dict["birth_date"])
        position = PlayerPosition(player_dict["position"])
        
        player = Player(
            first_name=player_dict["first_name"],
            last_name=player_dict["last_name"],
            nationality=player_dict["nationality"],
            birth_date=birth_date,
            position=position
        )
        
        # Ripristina ID
        player.id = player_dict["id"]
        
        # Posizioni secondarie
        player.secondary_positions = [
            PlayerPosition(pos) for pos in player_dict.get("secondary_positions", [])
        ]
        
        # Piede preferito e numero maglia
        player.foot_preference = FootPreference(player_dict["foot_preference"])
        player.shirt_number = player_dict.get("shirt_number")
        
        # Attributi
        attributes_data = player_dict["attributes"]
        for attr_name, value in attributes_data.items():
            setattr(player.attributes, attr_name, value)
        
        player.potential = player_dict["potential"]
        
        # Status
        player.injury_status = InjuryStatus(player_dict["injury_status"])
        player.injury_days_left = player_dict["injury_days_left"]
        player.morale = player_dict["morale"]
        player.fitness = player_dict["fitness"]
        
        # Statistiche
        stats_data = player_dict["current_season_stats"]
        player.current_season_stats = PlayerStats(
            presenze=stats_data["presenze"],
            minuti_giocati=stats_data["minuti_giocati"],
            gol=stats_data["gol"],
            assist=stats_data["assist"],
            cartellini_gialli=stats_data["cartellini_gialli"],
            cartellini_rossi=stats_data["cartellini_rossi"],
            voto_medio=stats_data["voto_medio"]
        )
        
        # Contratto
        contract_data = player_dict["contract"]
        player.contract = PlayerContract(
            stipendio_annuale=contract_data["stipendio_annuale"],
            data_scadenza=date.fromisoformat(contract_data["data_scadenza"]),
            clausola_rescissoria=contract_data["clausola_rescissoria"],
            status=ContractStatus(contract_data["status"])
        )
        
        # Club
        player.current_club = player_dict.get("current_club")
        player.is_on_loan = player_dict.get("is_on_loan", False)
        player.loan_club = player_dict.get("loan_club")
        
        # Market value (ricalcolato)
        player._market_value = player._calculate_market_value()
        
        return player
    
    def list_saved_squads(self) -> List[Dict]:
        """Lista tutti i file di rose salvati"""
        
        saved_files = []
        
        for file_path in self.squads_dir.glob("*.json"):
            try:
                data = load_json_file(file_path)
                if data and "metadata" in data:
                    metadata = data["metadata"]
                    saved_files.append({
                        "filename": file_path.name,
                        "generated_at": metadata.get("generated_at", "N/A"),
                        "total_countries": metadata.get("total_countries", 0),
                        "total_teams": metadata.get("total_teams", 0),
                        "total_players": metadata.get("total_players", 0),
                        "file_size_mb": file_path.stat().st_size / (1024 * 1024)
                    })
            except Exception as e:
                logger.warning(f"Errore lettura metadata file {file_path.name}: {e}")
        
        # Ordina per data di generazione (più recenti prima)
        saved_files.sort(key=lambda x: x["generated_at"], reverse=True)
        
        return saved_files
    
    def get_squad_summary(self, squads_data: Dict[str, Dict[str, List[Player]]]) -> Dict:
        """Genera statistiche riassuntive delle rose"""
        
        summary = {
            "countries": len(squads_data),
            "leagues": sum(len(leagues) for leagues in squads_data.values()),
            "teams": 0,
            "total_players": 0,
            "by_country": {},
            "overall_distribution": {f"{i}-{i+1}": 0 for i in range(5, 20)},
            "age_distribution": {f"{i}-{i+4}": 0 for i in range(16, 40, 5)},
            "nationality_distribution": {}
        }
        
        for country, leagues in squads_data.items():
            country_stats = {
                "leagues": len(leagues),
                "teams": 0,
                "players": 0,
                "avg_overall": 0,
                "avg_age": 0
            }
            
            country_overalls = []
            country_ages = []
            
            for league_name, teams in leagues.items():
                country_stats["teams"] += len(teams)
                summary["teams"] += len(teams)
                
                for team_name, players in teams.items():
                    country_stats["players"] += len(players)
                    summary["total_players"] += len(players)
                    
                    for player in players:
                        # Overall distribution
                        overall_bracket = f"{player.overall_rating}-{player.overall_rating+1}"
                        if overall_bracket in summary["overall_distribution"]:
                            summary["overall_distribution"][overall_bracket] += 1
                        
                        # Age distribution
                        age_bracket = f"{(player.age//5)*5}-{(player.age//5)*5+4}"
                        if age_bracket in summary["age_distribution"]:
                            summary["age_distribution"][age_bracket] += 1
                        
                        # Nationality distribution
                        nationality = player.nationality
                        summary["nationality_distribution"][nationality] = \
                            summary["nationality_distribution"].get(nationality, 0) + 1
                        
                        country_overalls.append(player.overall_rating)
                        country_ages.append(player.age)
            
            if country_overalls:
                country_stats["avg_overall"] = sum(country_overalls) / len(country_overalls)
                country_stats["avg_age"] = sum(country_ages) / len(country_ages)
            
            summary["by_country"][country] = country_stats
        
        return summary

# Istanza globale del gestore persistenza
squad_persistence = SquadPersistenceManager()
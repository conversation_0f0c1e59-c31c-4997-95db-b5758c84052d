"""
Pannello classifiche campionati
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QGroupBox, QTabWidget, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal

from ..components.data_table import DataTable
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR,
                           MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class StandingsPanel(QWidget):
    """Pannello per visualizzare le classifiche"""

    team_selected = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.standings_data = {}
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        self.create_panel_header(main_layout)

        # Tab widget per diverse competizioni
        self.tab_widget = QTabWidget()

        # Tab Serie A
        self.create_serie_a_tab()

        # Tab Coppa Italia
        self.create_cup_tab()

        # Tab Competizioni Europee
        self.create_european_tab()

        main_layout.addWidget(self.tab_widget)

    def create_panel_header(self, parent_layout):
        """Crea l'header del pannello"""
        header_layout = QHBoxLayout()

        # Titolo
        title_label = QLabel("Classifiche")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Giornata corrente
        self.matchday_label = QLabel("3ª Giornata")
        self.matchday_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                color: {COLOR_SUCCESS};
                padding: 5px 10px;
                border: 1px solid {COLOR_SUCCESS};
                border-radius: 4px;
            }}
        """)
        header_layout.addWidget(self.matchday_label)

        parent_layout.addLayout(header_layout)

    def create_serie_a_tab(self):
        """Crea la tab Serie A"""
        serie_a_widget = QWidget()
        layout = QVBoxLayout(serie_a_widget)

        # Tabella classifica
        columns = [
            {"key": "posizione", "title": "Pos", "width": 40, "align": "center"},
            {"key": "squadra", "title": "Squadra", "width": 200, "stretch": True},
            {"key": "punti", "title": "Pt", "width": 40, "align": "center"},
            {"key": "giocate", "title": "G", "width": 40, "align": "center"},
            {"key": "vinte", "title": "V", "width": 40, "align": "center"},
            {"key": "pareggiate", "title": "N", "width": 40, "align": "center"},
            {"key": "perse", "title": "P", "width": 40, "align": "center"},
            {"key": "gf", "title": "GF", "width": 40, "align": "center"},
            {"key": "gs", "title": "GS", "width": 40, "align": "center"},
            {"key": "dr", "title": "DR", "width": 50, "align": "center"}
        ]

        self.serie_a_table = DataTable(columns)
        self.serie_a_table.row_selected.connect(self.on_team_selected)
        layout.addWidget(self.serie_a_table)

        # Legenda
        self.create_legend(layout)

        # Carica dati Serie A
        self.load_serie_a_standings()

        self.tab_widget.addTab(serie_a_widget, "🏆 Serie A")

    def create_cup_tab(self):
        """Crea la tab Coppa Italia"""
        cup_widget = QWidget()
        layout = QVBoxLayout(cup_widget)

        # Info fase corrente
        phase_label = QLabel("Fase: Primo Turno")
        phase_label.setFont(QFont(MAIN_FONT_FAMILY, 12, QFont.Weight.Bold))
        phase_label.setStyleSheet(f"color: {COLOR_PRIMARY}; padding: 10px;")
        layout.addWidget(phase_label)

        # Placeholder per tabellone
        bracket_label = QLabel("Tabellone Coppa Italia")
        bracket_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        bracket_label.setStyleSheet("color: #666; font-style: italic; padding: 50px;")
        layout.addWidget(bracket_label)

        layout.addStretch()

        self.tab_widget.addTab(cup_widget, "🏆 Coppa Italia")

    def create_european_tab(self):
        """Crea la tab competizioni europee"""
        european_widget = QWidget()
        layout = QVBoxLayout(european_widget)

        # Selezione competizione
        comp_layout = QHBoxLayout()
        comp_layout.addWidget(QLabel("Competizione:"))
        
        self.european_combo = QComboBox()
        self.european_combo.addItems([
            "Champions League", "Europa League", "Conference League"
        ])
        comp_layout.addWidget(self.european_combo)
        comp_layout.addStretch()
        
        layout.addLayout(comp_layout)

        # Placeholder per gironi
        groups_label = QLabel("Gironi Europei")
        groups_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        groups_label.setStyleSheet("color: #666; font-style: italic; padding: 50px;")
        layout.addWidget(groups_label)

        layout.addStretch()

        self.tab_widget.addTab(european_widget, "🌍 Europa")

    def create_legend(self, parent_layout):
        """Crea la legenda per le posizioni"""
        legend_group = QGroupBox("Legenda")
        legend_layout = QHBoxLayout(legend_group)

        legends = [
            ("🟢", "Champions League", COLOR_SUCCESS),
            ("🔵", "Europa League", COLOR_PRIMARY),
            ("🟡", "Conference League", COLOR_WARNING),
            ("🔴", "Retrocessione", COLOR_ERROR)
        ]

        for symbol, description, color in legends:
            legend_item = QLabel(f"{symbol} {description}")
            legend_item.setStyleSheet(f"color: {color}; font-size: 10px; margin: 2px;")
            legend_layout.addWidget(legend_item)

        legend_layout.addStretch()
        parent_layout.addWidget(legend_group)

    def load_serie_a_standings(self):
        """Carica la classifica Serie A"""
        sample_standings = [
            {
                "posizione": 1, "squadra": "🟢 Inter Milan", "punti": 9,
                "giocate": 3, "vinte": 3, "pareggiate": 0, "perse": 0,
                "gf": 8, "gs": 2, "dr": "+6"
            },
            {
                "posizione": 2, "squadra": "🟢 Juventus", "punti": 7,
                "giocate": 3, "vinte": 2, "pareggiate": 1, "perse": 0,
                "gf": 6, "gs": 2, "dr": "+4"
            },
            {
                "posizione": 3, "squadra": "🟢 AC Milan", "punti": 6,
                "giocate": 3, "vinte": 2, "pareggiate": 0, "perse": 1,
                "gf": 5, "gs": 3, "dr": "+2"
            },
            {
                "posizione": 4, "squadra": "🟢 Napoli", "punti": 6,
                "giocate": 3, "vinte": 2, "pareggiate": 0, "perse": 1,
                "gf": 4, "gs": 3, "dr": "+1"
            },
            {
                "posizione": 5, "squadra": "🔵 AS Roma", "punti": 5,
                "giocate": 3, "vinte": 1, "pareggiate": 2, "perse": 0,
                "gf": 4, "gs": 3, "dr": "+1"
            },
            {
                "posizione": 6, "squadra": "🔵 Lazio", "punti": 4,
                "giocate": 3, "vinte": 1, "pareggiate": 1, "perse": 1,
                "gf": 3, "gs": 3, "dr": "0"
            },
            {
                "posizione": 7, "squadra": "🟡 Atalanta", "punti": 4,
                "giocate": 3, "vinte": 1, "pareggiate": 1, "perse": 1,
                "gf": 5, "gs": 4, "dr": "+1"
            },
            {
                "posizione": 8, "squadra": "Fiorentina", "punti": 3,
                "giocate": 3, "vinte": 1, "pareggiate": 0, "perse": 2,
                "gf": 3, "gs": 4, "dr": "-1"
            },
            {
                "posizione": 18, "squadra": "🔴 Venezia", "punti": 1,
                "giocate": 3, "vinte": 0, "pareggiate": 1, "perse": 2,
                "gf": 1, "gs": 5, "dr": "-4"
            },
            {
                "posizione": 19, "squadra": "🔴 Monza", "punti": 1,
                "giocate": 3, "vinte": 0, "pareggiate": 1, "perse": 2,
                "gf": 2, "gs": 6, "dr": "-4"
            },
            {
                "posizione": 20, "squadra": "🔴 Lecce", "punti": 0,
                "giocate": 3, "vinte": 0, "pareggiate": 0, "perse": 3,
                "gf": 1, "gs": 7, "dr": "-6"
            }
        ]

        self.standings_data['serie_a'] = sample_standings
        self.serie_a_table.set_data(sample_standings)

    def on_team_selected(self, row_index, team_data):
        """Gestisce la selezione di una squadra"""
        self.team_selected.emit(team_data)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'standings',
            'current_tab': self.tab_widget.currentIndex(),
            'standings_data': self.standings_data
        }

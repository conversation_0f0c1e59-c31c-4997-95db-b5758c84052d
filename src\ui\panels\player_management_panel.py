"""
Pannello gestione giocatori con tabella e filtri
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QComboBox, QGroupBox, QSplitter)
from PyQt6.QtCore import Qt, pyqtSignal

from ..components.data_table import DataTable
from ..components.info_display import InfoDisplay
from ..components.action_buttons import PlayerActions
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class PlayerManagementPanel(QWidget):
    """Pannello per la gestione completa dei giocatori"""

    player_selected = pyqtSignal(dict)
    action_requested = pyqtSignal(str, dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.players_data = []
        self.selected_player = None
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        self.create_panel_header(main_layout)

        # Splitter principale (lista giocatori | dettagli)
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Lato sinistro - Lista giocatori
        self.create_players_list_section()

        # Lato destro - Dettagli giocatore
        self.create_player_details_section()

        self.main_splitter.addWidget(self.players_list_widget)
        self.main_splitter.addWidget(self.player_details_widget)

        # Imposta proporzioni splitter
        self.main_splitter.setSizes([600, 400])

        main_layout.addWidget(self.main_splitter)

    def create_panel_header(self, parent_layout):
        """Crea l'header del pannello"""
        header_layout = QHBoxLayout()

        # Titolo
        title_label = QLabel("Gestione Giocatori")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Filtri rapidi
        self.create_quick_filters(header_layout)

        parent_layout.addLayout(header_layout)

    def create_quick_filters(self, parent_layout):
        """Crea i filtri rapidi"""
        # Filtro per ruolo
        parent_layout.addWidget(QLabel("Ruolo:"))
        self.role_filter = QComboBox()
        self.role_filter.addItems([
            "Tutti", "Portieri", "Difensori", "Centrocampisti", "Attaccanti"
        ])
        self.role_filter.currentTextChanged.connect(self.apply_filters)
        parent_layout.addWidget(self.role_filter)

        # Filtro per stato
        parent_layout.addWidget(QLabel("Stato:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "Tutti", "Disponibili", "Infortunati", "Squalificati"
        ])
        self.status_filter.currentTextChanged.connect(self.apply_filters)
        parent_layout.addWidget(self.status_filter)

    def create_players_list_section(self):
        """Crea la sezione lista giocatori"""
        self.players_list_widget = QGroupBox("Rosa Giocatori")
        list_layout = QVBoxLayout(self.players_list_widget)

        # Configura colonne tabella
        columns = [
            {"key": "nome", "title": "Nome", "width": 150, "stretch": True},
            {"key": "cognome", "title": "Cognome", "width": 150},
            {"key": "eta", "title": "Età", "width": 50, "align": "center"},
            {"key": "posizione", "title": "Ruolo", "width": 80, "align": "center"},
            {"key": "valore", "title": "Valore", "width": 100, "align": "right",
             "formatter": lambda x: f"€{x:,.0f}" if isinstance(x, (int, float)) else "N/A"},
            {"key": "contratto", "title": "Contratto", "width": 80, "align": "center"},
            {"key": "stato", "title": "Stato", "width": 80, "align": "center"}
        ]

        # Crea tabella
        self.players_table = DataTable(columns)
        self.players_table.row_selected.connect(self.on_player_selected)
        self.players_table.row_double_clicked.connect(self.on_player_double_clicked)

        list_layout.addWidget(self.players_table)

        # Stile del gruppo
        self.players_list_widget.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }}
        """)

    def create_player_details_section(self):
        """Crea la sezione dettagli giocatore"""
        self.player_details_widget = QGroupBox("Dettagli Giocatore")
        details_layout = QVBoxLayout(self.player_details_widget)

        # Info display per giocatore
        self.player_info_display = PlayerInfoDisplay()
        details_layout.addWidget(self.player_info_display)

        # Azioni giocatore
        self.player_actions = PlayerActions()
        self.player_actions.action_triggered.connect(self.on_player_action)
        details_layout.addWidget(self.player_actions)

        details_layout.addStretch()

        # Stile del gruppo
        self.player_details_widget.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }}
        """)

        # Inizialmente disabilitato
        self.player_details_widget.setEnabled(False)

    def apply_filters(self):
        """Applica i filtri alla lista giocatori"""
        # Implementazione filtri (placeholder per ora)
        # Filtra i dati in base ai combobox
        filtered_data = self.players_data.copy()

        # Applica filtro ruolo
        role_filter = self.role_filter.currentText()
        if role_filter != "Tutti":
            # Logica filtro ruolo
            pass

        # Applica filtro stato
        status_filter = self.status_filter.currentText()
        if status_filter != "Tutti":
            # Logica filtro stato
            pass

        self.players_table.set_data(filtered_data)

    def on_player_selected(self, row_index, player_data):
        """Gestisce la selezione di un giocatore"""
        self.selected_player = player_data
        self.player_info_display.set_data(player_data)
        self.player_details_widget.setEnabled(True)
        self.player_selected.emit(player_data)

    def on_player_double_clicked(self, row_index, player_data):
        """Gestisce il doppio click su un giocatore"""
        self.action_requested.emit("view_player_detail", player_data)

    def on_player_action(self, action, extra_data):
        """Gestisce le azioni sui giocatori"""
        if self.selected_player:
            action_data = {
                'action': action,
                'player': self.selected_player,
                'extra': extra_data
            }
            self.action_requested.emit(action, action_data)

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data
        self.load_players_data()

    def load_players_data(self):
        """Carica i dati dei giocatori (placeholder)"""
        # Genera dati giocatori di esempio
        self.players_data = self.generate_sample_players()
        self.apply_filters()

    def generate_sample_players(self):
        """Genera giocatori di esempio"""
        sample_players = [
            {
                "nome": "Marco", "cognome": "Rossi", "eta": 25,
                "posizione": "DC", "valore": 5000000, "contratto": "2027",
                "stato": "Disponibile"
            },
            {
                "nome": "Luca", "cognome": "Bianchi", "eta": 28,
                "posizione": "CC", "valore": 8000000, "contratto": "2026",
                "stato": "Disponibile"
            },
            {
                "nome": "Andrea", "cognome": "Verdi", "eta": 22,
                "posizione": "C", "valore": 12000000, "contratto": "2028",
                "stato": "Infortunato"
            },
            {
                "nome": "Stefano", "cognome": "Neri", "eta": 30,
                "posizione": "P", "valore": 3000000, "contratto": "2025",
                "stato": "Disponibile"
            }
        ]
        return sample_players

    def refresh_display(self):
        """Aggiorna la visualizzazione"""
        self.load_players_data()

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'player_management',
            'selected_player': self.selected_player,
            'players_count': len(self.players_data)
        }


class PlayerInfoDisplay(InfoDisplay):
    """Display specifico per informazioni giocatore"""

    def __init__(self, parent=None):
        super().__init__("", parent)

    def refresh_display(self):
        """Aggiorna il display con i dati del giocatore"""
        if not self.info_data:
            self.clear_content()
            no_selection = QLabel("Seleziona un giocatore per vedere i dettagli")
            no_selection.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_selection.setStyleSheet("color: #999; font-style: italic; padding: 20px;")
            self.add_custom_widget(no_selection)
            return

        self.clear_content()

        # Nome completo evidenziato
        nome_completo = f"{self.info_data.get('nome', '')} {self.info_data.get('cognome', '')}"
        self.add_highlight_value("", nome_completo, COLOR_PRIMARY)

        self.add_separator()

        # Info base
        info_base = {
            'Età': self.info_data.get('eta', 'N/A'),
            'Posizione': self.info_data.get('posizione', 'N/A'),
            'Valore': f"€{self.info_data.get('valore', 0):,.0f}",
            'Contratto': self.info_data.get('contratto', 'N/A')
        }

        self.add_info_grid(info_base, columns=2)

        self.add_separator()

        # Stato
        stato = self.info_data.get('stato', 'Sconosciuto')
        color = COLOR_PRIMARY if stato == 'Disponibile' else '#dc3545'
        self.add_highlight_value("Stato", stato, color)

        # Placeholder per attributi
        if 'attributi' in self.info_data:
            self.add_separator()
            self.add_stats_bars(self.info_data['attributi'])
"""
Configuration file for the game.
"""

# List of top teams for determining important matches
TOP_TEAMS = [
    "Juventus", "Inter Milan", "AC Milan", "Napoli", "AS Roma", "Lazio", "Atalanta"
]

# List of derbies
DERBIES = {
    "milan": ["AC Milan", "Inter Milan"],
    "rome": ["AS Roma", "Lazio"],
    "turin": ["Juventus", "Torino"]
}

# Logging configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
LOG_FILE = "data/football_manager.log"

# UI Colors
COLOR_PRIMARY = "#007bff"
COLOR_SECONDARY = "#6c757d"
COLOR_BACKGROUND = "#f8f9fa"
COLOR_SURFACE = "#ffffff"
COLOR_BORDER = "#dee2e6"
COLOR_TEXT = "#000000"  # Nero puro per massima visibilità
COLOR_TEXT_SECONDARY = "#555555"  # Grigio scuro per testo secondario
COLOR_SUCCESS = "#28a745"
COLOR_WARNING = "#ffc107"
COLOR_ERROR = "#dc3545"

# UI Fonts
MAIN_FONT_FAMILY = "Segoe UI"
MAIN_FONT_SIZE = 9
HEADER_FONT_SIZE = 10
TITLE_FONT_SIZE = 14

# Player configuration
PLAYER_POSITIONS = {
    "GK": "Goalkeeper",
    "DF": "Defender",
    "MF": "Midfielder",
    "FW": "Forward"
}

PLAYER_ATTRIBUTES = [
    "tecnica", "velocita", "forza", "resistenza", "tiro", "passaggio", "dribbling", "difesa", "portiere", "mentalita"
]

PLAYER_AGE_RANGES = {
    "youth": (16, 21),
    "prime": (22, 29),
    "veteran": (30, 38)
}

# Team Budgets
STARTING_BUDGETS = {
    1: (50_000_000, 100_000_000), # Top tier (e.g., Serie A)
    2: (10_000_000, 30_000_000),  # Mid tier (e.g., Serie B)
    3: (1_000_000, 5_000_000)     # Lower tier (e.g., Serie C)
}

# File paths
from pathlib import Path

BASE_DIR = Path(__file__).parent.parent.parent  # Project root
PLAYER_NAMES_FILE = BASE_DIR / "player_names.json"
FIFA_CODES_FILE = BASE_DIR / "fifa_country_codes.json"
FIFA_CONFEDERATIONS_FILE = BASE_DIR / "fifa_confederations.json"

# NUOVO: Configurazioni sistema squadre avanzato
SQUAD_GENERATION_CONFIG = {
    "use_reputation_system": True,  # Usa il nuovo sistema basato su reputazione
    "auto_save_squads": True,       # Salva automaticamente le squadre generate
    "load_pre_generated": True,     # Carica squadre pre-generate se disponibili
    "default_squad_size": 25,       # Dimensione default rose
    "enable_persistence": True      # Abilita sistema di persistenza
}

# Finestra principale
WINDOW_TITLE = "Football Manager Italiano"
WINDOW_DEFAULT_WIDTH = 1200
WINDOW_DEFAULT_HEIGHT = 800
WINDOW_MIN_WIDTH = 800
WINDOW_MIN_HEIGHT = 600

# Risultati stagione precedente per coppe (2023/24)
# Questi sono i risultati storici della stagione precedente
SERIE_A_CHAMPION_2024 = "Napoli"  # Campione Serie A 2023/24
SERIE_A_RUNNER_UP_2024 = "Inter Milan"  # Vice-campione Serie A 2023/24
COPPA_ITALIA_WINNER_2024 = "Bologna"  # Vincitore Coppa Italia 2023/24
COPPA_ITALIA_FINALIST_2024 = "Atalanta"  # Finalista Coppa Italia 2023/24

# Classifica Serie A 2023/24 (per qualificazioni Coppa Italia)
SERIE_A_FINAL_TABLE_2024 = [
    "Napoli", "Inter Milan", "AC Milan", "Juventus", "AS Roma", "Lazio",
    "Atalanta", "Fiorentina", "Bologna", "Torino", "Udinese", "Sassuolo",
    "Empoli", "Monza", "Genoa", "Lecce", "Cagliari", "Verona", "Spezia", "Cremonese"
]
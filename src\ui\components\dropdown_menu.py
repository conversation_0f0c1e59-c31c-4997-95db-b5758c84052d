"""
Menu a tendina personalizzato per la dashboard
"""

from PyQt6.QtWidgets import (QPushButton, QMenu, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal, QPropertyAnimation, QRect
from PyQt6.QtGui import QFont, QIcon

from ...core.config import (COLOR_PRIMARY, COLOR_SURFACE, COLOR_BORDER,
                           COLOR_TEXT, MAIN_FONT_FAMILY, MAIN_FONT_SIZE)


class DropdownMenu(QPushButton):
    """Menu a tendina personalizzato per la dashboard"""

    action_triggered = pyqtSignal(str)

    def __init__(self, title, parent=None):
        super().__init__(title, parent)
        self.menu_title = title
        self.menu_actions = []
        self.setup_menu()
        self.setup_style()

    def setup_menu(self):
        """Configura il menu a tendina"""
        self.dropdown_menu = QMenu(self)
        self.setMenu(self.dropdown_menu)

        # Configura il comportamento del pulsante
        self.setFixedHeight(40)
        self.setMinimumWidth(100)
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)

    def setup_style(self):
        """Configura lo stile del menu"""
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SURFACE};
                color: {COLOR_TEXT};
                border: 1px solid {COLOR_BORDER};
                border-radius: 6px;
                padding: 8px 16px;
                font-family: {MAIN_FONT_FAMILY};
                font-size: {MAIN_FONT_SIZE + 1}px;
                font-weight: bold;
                text-align: left;
            }}

            QPushButton:hover {{
                background-color: #e9ecef;
                border-color: {COLOR_PRIMARY};
            }}

            QPushButton:pressed {{
                background-color: #dee2e6;
            }}

            QPushButton::menu-indicator {{
                image: none;
                width: 0px;
            }}

            QMenu {{
                background-color: {COLOR_SURFACE};
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                padding: 4px;
                margin: 2px;
            }}

            QMenu::item {{
                background-color: transparent;
                color: {COLOR_TEXT};
                padding: 8px 16px;
                border-radius: 4px;
                margin: 1px;
                font-family: {MAIN_FONT_FAMILY};
                font-size: {MAIN_FONT_SIZE}px;
            }}

            QMenu::item:selected {{
                background-color: {COLOR_PRIMARY};
                color: white;
            }}

            QMenu::item:pressed {{
                background-color: #0056b3;
                color: white;
            }}

            QMenu::separator {{
                height: 1px;
                background-color: {COLOR_BORDER};
                margin: 4px 8px;
            }}
        """)

        # Aggiorna il testo con freccia
        self.setText(f"{self.menu_title} ▼")

    def add_action(self, text, callback=None, icon=None, separator_before=False):
        """Aggiunge un'azione al menu"""
        if separator_before and self.menu_actions:
            self.dropdown_menu.addSeparator()

        action = self.dropdown_menu.addAction(text)

        if icon:
            action.setIcon(icon)

        if callback:
            action.triggered.connect(callback)

        # Mantieni riferimento per eventuale uso futuro
        self.menu_actions.append({
            'text': text,
            'action': action,
            'callback': callback
        })

        return action

    def add_submenu(self, title, parent_menu=None):
        """Aggiunge un sottomenu"""
        if parent_menu is None:
            parent_menu = self.dropdown_menu

        submenu = parent_menu.addMenu(title)
        submenu.setStyleSheet(self.dropdown_menu.styleSheet())

        return submenu

    def clear_actions(self):
        """Rimuove tutte le azioni dal menu"""
        self.dropdown_menu.clear()
        self.menu_actions.clear()

    def set_enabled_action(self, text, enabled):
        """Abilita/disabilita un'azione specifica"""
        for action_data in self.menu_actions:
            if action_data['text'] == text:
                action_data['action'].setEnabled(enabled)
                break

    def get_action_count(self):
        """Restituisce il numero di azioni nel menu"""
        return len(self.menu_actions)

    def set_badge(self, count):
        """Aggiunge un badge numerico al menu (per notifiche)"""
        if count > 0:
            badge_text = f" ({count})"
            self.setText(f"{self.menu_title}{badge_text} ▼")
        else:
            self.setText(f"{self.menu_title} ▼")


class MenuGroup(QWidget):
    """Gruppo di menu a tendina correlati"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.menus = []
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del gruppo"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(10)

    def add_menu(self, menu):
        """Aggiunge un menu al gruppo"""
        if isinstance(menu, DropdownMenu):
            self.menus.append(menu)
            self.layout.addWidget(menu)
            return menu
        else:
            raise ValueError("Il menu deve essere un'istanza di DropdownMenu")

    def create_menu(self, title):
        """Crea e aggiunge un nuovo menu al gruppo"""
        menu = DropdownMenu(title)
        return self.add_menu(menu)

    def get_menu(self, title):
        """Ottiene un menu per titolo"""
        for menu in self.menus:
            if menu.menu_title == title:
                return menu
        return None

    def clear_all_menus(self):
        """Pulisce tutti i menu del gruppo"""
        for menu in self.menus:
            menu.clear_actions()

    def set_enabled_group(self, enabled):
        """Abilita/disabilita tutti i menu del gruppo"""
        for menu in self.menus:
            menu.setEnabled(enabled)
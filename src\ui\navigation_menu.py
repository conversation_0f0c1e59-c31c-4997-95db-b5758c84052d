"""
Menu di navigazione principale per l'interfaccia Football Manager
Sistema di menu a tendina per macro aree funzionali
"""

from PyQt6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel,
                            QFrame, QSizePolicy, QSpacerItem)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from .components.dropdown_menu import DropdownMenu, MenuGroup
from ..core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                          COLOR_TEXT, MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class NavigationMenu(QWidget):
    """Menu di navigazione principale con macro aree"""

    navigation_requested = pyqtSignal(str, dict)  # area, parametri

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_area = None
        self.menu_groups = {}
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del menu"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Header del menu
        self.create_menu_header(main_layout)

        # Container menu principali
        self.create_main_menus(main_layout)

        # Stile del widget
        self.setStyleSheet(f"""
            NavigationMenu {{
                background-color: {COLOR_SURFACE};
                border-bottom: 2px solid {COLOR_BORDER};
            }}
        """)

    def create_menu_header(self, parent_layout):
        """Crea l'header del menu di navigazione"""
        header_frame = QFrame()
        header_frame.setFixedHeight(50)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLOR_PRIMARY};
                border: none;
            }}
        """)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)

        # Titolo applicazione
        title_label = QLabel("Football Manager Italiano")
        title_label.setFont(QFont(MAIN_FONT_FAMILY, HEADER_FONT_SIZE, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Info utente/sessione (placeholder)
        user_label = QLabel("Modalità Manager")
        user_label.setStyleSheet("color: white; font-size: 10px;")
        header_layout.addWidget(user_label)

        parent_layout.addWidget(header_frame)

    def create_main_menus(self, parent_layout):
        """Crea i menu principali delle macro aree"""
        menu_frame = QFrame()
        menu_frame.setFixedHeight(60)
        menu_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLOR_SURFACE};
                border-bottom: 1px solid {COLOR_BORDER};
            }}
        """)

        menu_layout = QHBoxLayout(menu_frame)
        menu_layout.setContentsMargins(20, 10, 20, 10)
        menu_layout.setSpacing(15)

        # Crea gruppo menu
        self.main_menu_group = MenuGroup()

        # Menu Squadra
        squadra_menu = self.main_menu_group.create_menu("Squadra")
        self.setup_squadra_menu(squadra_menu)

        # Menu Partite
        partite_menu = self.main_menu_group.create_menu("Partite")
        self.setup_partite_menu(partite_menu)

        # Menu Mercato
        mercato_menu = self.main_menu_group.create_menu("Mercato")
        self.setup_mercato_menu(mercato_menu)

        # Menu Gestione
        gestione_menu = self.main_menu_group.create_menu("Gestione")
        self.setup_gestione_menu(gestione_menu)

        # Menu Simulazione
        simulazione_menu = self.main_menu_group.create_menu("Simulazione")
        self.setup_simulazione_menu(simulazione_menu)

        menu_layout.addWidget(self.main_menu_group)
        menu_layout.addStretch()

        parent_layout.addWidget(menu_frame)

    def setup_squadra_menu(self, menu):
        """Configura il menu Squadra"""
        menu.add_action("Panoramica", lambda: self.navigate_to('team_overview'))
        menu.add_action("Rosa Giocatori", lambda: self.navigate_to('player_management'))
        menu.add_action("Tattiche", lambda: self.navigate_to('tactics'))
        menu.add_action("Strutture", lambda: self.navigate_to('facilities'))

    def setup_partite_menu(self, menu):
        """Configura il menu Partite"""
        menu.add_action("Centro Partite", lambda: self.navigate_to('match_center'))
        menu.add_action("Calendario", lambda: self.navigate_to('calendar'))
        menu.add_action("Risultati", lambda: self.navigate_to('results'))
        menu.add_action("Classifiche", lambda: self.navigate_to('standings'))

    def setup_mercato_menu(self, menu):
        """Configura il menu Mercato"""
        menu.add_action("Trasferimenti", lambda: self.navigate_to('transfers'))
        menu.add_action("Contratti", lambda: self.navigate_to('contracts'))
        menu.add_action("Prestiti", lambda: self.navigate_to('loans'))
        menu.add_action("Scouting", lambda: self.navigate_to('scouting'))

    def setup_gestione_menu(self, menu):
        """Configura il menu Gestione"""
        menu.add_action("Finanze", lambda: self.navigate_to('finances'))
        menu.add_action("Statistiche", lambda: self.navigate_to('statistics'))
        menu.add_action("Staff", lambda: self.navigate_to('staff'))
        menu.add_action("Impostazioni", lambda: self.navigate_to('settings'))

    def setup_simulazione_menu(self, menu):
        """Configura il menu Simulazione"""
        menu.add_action("Controlli", lambda: self.navigate_to('simulation'))
        menu.add_action("Timeline", lambda: self.navigate_to('timeline'))
        menu.add_action("Notizie", lambda: self.navigate_to('news'))

    def navigate_to(self, area, params=None):
        """Naviga verso un'area specifica"""
        if params is None:
            params = {}

        self.current_area = area
        self.navigation_requested.emit(area, params)

    def set_current_area(self, area):
        """Imposta l'area corrente (per evidenziazione)"""
        self.current_area = area
        # Qui si potrebbe aggiungere logica per evidenziare il menu corrente

    def get_current_area(self):
        """Restituisce l'area corrente"""
        return self.current_area

    def enable_menu_area(self, area, enabled):
        """Abilita/disabilita un'area del menu"""
        # Implementazione per abilitare/disabilitare specifiche aree
        pass

    def set_menu_badge(self, menu_name, count):
        """Imposta un badge numerico su un menu"""
        menu = self.main_menu_group.get_menu(menu_name)
        if menu:
            menu.set_badge(count)

    def refresh_menus(self):
        """Aggiorna tutti i menu"""
        # Implementazione per aggiornare dinamicamente i menu
        pass


class BreadcrumbNavigation(QWidget):
    """Navigazione breadcrumb per mostrare il percorso corrente"""

    breadcrumb_clicked = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.breadcrumb_path = []
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia breadcrumb"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(20, 5, 20, 5)
        self.layout.setSpacing(5)

        self.setStyleSheet(f"""
            BreadcrumbNavigation {{
                background-color: #f8f9fa;
                border-bottom: 1px solid {COLOR_BORDER};
            }}
        """)

    def set_path(self, path_items):
        """Imposta il percorso breadcrumb"""
        # Pulisce il layout esistente
        while self.layout.count():
            child = self.layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        self.breadcrumb_path = path_items

        for i, item in enumerate(path_items):
            if i > 0:
                # Separatore
                separator = QLabel(" > ")
                separator.setStyleSheet("color: #6c757d;")
                self.layout.addWidget(separator)

            # Item breadcrumb
            if i == len(path_items) - 1:
                # Ultimo item (corrente) - non cliccabile
                label = QLabel(item)
                label.setStyleSheet(f"color: {COLOR_PRIMARY}; font-weight: bold;")
            else:
                # Item cliccabile
                label = QLabel(f'<a href="{item}" style="color: {COLOR_PRIMARY}; text-decoration: none;">{item}</a>')
                label.setOpenExternalLinks(False)
                label.linkActivated.connect(self.breadcrumb_clicked.emit)

            self.layout.addWidget(label)

        # Spacer finale
        spacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.layout.addItem(spacer)

    def clear_path(self):
        """Pulisce il percorso breadcrumb"""
        self.set_path([])

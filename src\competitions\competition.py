"""
Competition manager per gestione campionati
"""
from typing import List, Dict, Optional, Tuple, TYPE_CHECKING
from dataclasses import dataclass, field
from enum import Enum
import random

from ..core.utils import logger
from .season import Season
from .match_engine import match_simulator

# Importazioni condizionali per evitare circular imports
if TYPE_CHECKING:
    from .calendar import Match, MatchCalendar

class CompetitionType(Enum):
    """Tipi di competizione"""
    LEAGUE = "Campionato"
    CUP = "Coppa"
    FRIENDLY = "Amichevole"

class CompetitionLevel(Enum):
    """Livelli competizione italiana"""
    SERIE_A = 1
    SERIE_B = 2
    SERIE_C = 3

@dataclass
class LeagueStanding:
    """Posizione in classifica"""
    position: int
    team_name: str
    matches_played: int = 0
    wins: int = 0
    draws: int = 0
    losses: int = 0
    goals_for: int = 0
    goals_against: int = 0
    points: int = 0

    @property
    def goal_difference(self) -> int:
        return self.goals_for - self.goals_against

    @property
    def form_guide(self) -> str:
        """Ultimi 5 risultati (V/N/S)"""
        # Implementazione semplificata - in un gioco reale tracceremmo questo
        return "VVNSS"

    def update_from_match(self, goals_for: int, goals_against: int):
        """Aggiorna statistiche da risultato partita"""
        self.matches_played += 1
        self.goals_for += goals_for
        self.goals_against += goals_against

        if goals_for > goals_against:
            self.wins += 1
            self.points += 3
        elif goals_for == goals_against:
            self.draws += 1
            self.points += 1
        else:
            self.losses += 1

class Competition:
    """Classe base per competizioni"""

    def __init__(self, name: str, season: Season, teams: List[str]):
        self.name = name
        self.season = season
        self.teams = teams.copy()
        self.matches: List = []  # Tipo generico per evitare circular import

        # Statistiche
        self.current_matchday = 0
        self.max_matchdays = self._calculate_max_matchdays()

        # Dati squadre per simulazione realistica
        self.teams_data: Optional[Dict] = None

        logger.info(f"Creata competizione: {name} con {len(teams)} squadre")

    def _calculate_max_matchdays(self) -> int:
        """Calcola numero massimo giornate"""
        # Default: all'italiana andata e ritorno
        return (len(self.teams) - 1) * 2

    def has_match_on_matchday(self, matchday: int) -> bool:
        """Verifica se ci sono partite in una giornata"""
        return any(m.matchday == matchday for m in self.matches)

    def get_matches_for_matchday(self, matchday: int) -> List:
        """Restituisce partite di una giornata"""
        return [m for m in self.matches if m.matchday == matchday]

    def set_teams_data(self, teams_data: Dict):
        """Imposta dati squadre per simulazione realistica"""
        self.teams_data = teams_data
        logger.debug(f"{self.name}: Impostati dati per {len(teams_data)} squadre")

    def get_team_players(self, team_name: str) -> List:
        """Restituisce giocatori di una squadra"""
        # Prima controlla nei dati principali
        if self.teams_data and team_name in self.teams_data:
            return self.teams_data[team_name]

        # Se non trovato, controlla se è una competizione UEFA con dati separati
        if hasattr(self, 'uefa_teams_data') and self.uefa_teams_data and team_name in self.uefa_teams_data:
            return self.uefa_teams_data[team_name]

        # Fallback: prova a ottenere dalla cache del generatore di squadre
        try:
            from ..players.team_squad_generator import team_squad_generator
            squad = team_squad_generator.get_team_squad(team_name)
            if squad and len(squad) >= 11:
                logger.debug(f"Trovati giocatori per {team_name} dalla cache del generatore")
                return squad
        except Exception as e:
            logger.debug(f"Impossibile accedere al generatore squadre: {e}")

        # Solo logga warning se non è una squadra europea conosciuta
        if not self._is_known_european_team(team_name):
            logger.warning(f"Dati squadra non trovati per {team_name}")

        return []

    def _is_known_european_team(self, team_name: str) -> bool:
        """Verifica se è una squadra europea conosciuta (per evitare warning inutili)"""
        # Lista di squadre europee famose che potrebbero non avere dati giocatori
        known_european_teams = {
            # Inghilterra
            'Manchester City', 'Arsenal', 'Liverpool', 'Chelsea', 'Manchester United',
            'Tottenham', 'Newcastle', 'Brighton', 'Aston Villa', 'West Ham',
            # Spagna
            'Real Madrid', 'Barcelona', 'Atletico Madrid', 'Sevilla', 'Real Sociedad',
            'Villarreal', 'Real Betis', 'Valencia', 'Athletic Bilbao', 'Getafe',
            # Germania
            'Bayern Munich', 'Borussia Dortmund', 'RB Leipzig', 'Bayer Leverkusen',
            'Eintracht Frankfurt', 'VfB Stuttgart', 'Borussia Mönchengladbach',
            # Francia
            'Paris Saint-Germain', 'Marseille', 'Monaco', 'Lyon', 'Lille',
            'Rennes', 'Nice', 'Lens', 'Strasbourg', 'Nantes',
            # Italia (squadre che partecipano a competizioni europee)
            'Inter Milan', 'AC Milan', 'Juventus', 'Napoli', 'AS Roma', 'Lazio',
            'Atalanta', 'Fiorentina', 'Bologna',
            # Altri paesi europei
            'Ajax', 'PSV', 'Feyenoord', 'Porto', 'Benfica', 'Sporting CP',
            'Club Brugge', 'Anderlecht', 'Red Bull Salzburg', 'Sturm Graz'
        }

        return team_name in known_european_teams

    def simulate_matchday(self, matchday: int) -> List[Dict]:
        """Simula partite di una giornata - implementato nelle sottoclassi"""
        raise NotImplementedError

class LeagueCompetition(Competition):
    """Competizione di campionato con classifica"""

    def __init__(self, name: str, season: Season, teams: List[str], level: CompetitionLevel):
        super().__init__(name, season, teams)
        self.level = level
        self.competition_type = CompetitionType.LEAGUE

        # Inizializza classifica
        self.standings: Dict[str, LeagueStanding] = {}
        for i, team in enumerate(teams):
            self.standings[team] = LeagueStanding(
                position=i + 1,
                team_name=team
            )

        # Promozioni/retrocessioni per livello
        self.promotion_spots, self.relegation_spots = self._get_promotion_relegation_spots()

    def _get_promotion_relegation_spots(self) -> Tuple[int, int]:
        """Restituisce posti promozione/retrocessione per livello"""
        spots_config = {
            CompetitionLevel.SERIE_A: (0, 3),    # 0 promozioni, 3 retrocessioni
            CompetitionLevel.SERIE_B: (3, 3),    # 3 promozioni, 3 retrocessioni
            CompetitionLevel.SERIE_C: (3, 0)     # 3 promozioni, 0 retrocessioni
        }
        return spots_config.get(self.level, (0, 0))

    def update_standings(self, match):
        """Aggiorna classifica dopo una partita"""
        if not match.played:
            return

        home_standing = self.standings[match.home_team]
        away_standing = self.standings[match.away_team]

        home_standing.update_from_match(match.home_score, match.away_score)
        away_standing.update_from_match(match.away_score, match.home_score)

        # Riordina classifica
        self._sort_standings()

    def _sort_standings(self):
        """Ordina classifica per punti, differenza reti, gol fatti"""
        sorted_standings = sorted(
            self.standings.values(),
            key=lambda s: (-s.points, -s.goal_difference, -s.goals_for, s.team_name)
        )

        # Aggiorna posizioni
        for i, standing in enumerate(sorted_standings):
            standing.position = i + 1

    def get_standings_table(self) -> List[LeagueStanding]:
        """Restituisce classifica ordinata"""
        return sorted(self.standings.values(), key=lambda s: s.position)

    def get_team_standing(self, team_name: str) -> Optional[LeagueStanding]:
        """Restituisce posizione squadra"""
        return self.standings.get(team_name)

    def get_promotion_zone_teams(self) -> List[str]:
        """Restituisce squadre in zona promozione"""
        if self.promotion_spots == 0:
            return []

        sorted_standings = self.get_standings_table()
        return [s.team_name for s in sorted_standings[:self.promotion_spots]]

    def get_relegation_zone_teams(self) -> List[str]:
        """Restituisce squadre in zona retrocessione"""
        if self.relegation_spots == 0:
            return []

        sorted_standings = self.get_standings_table()
        return [s.team_name for s in sorted_standings[-self.relegation_spots:]]

    def get_playoff_zone_teams(self) -> List[str]:
        """Restituisce squadre in zona playoff (Serie B)"""
        if self.level != CompetitionLevel.SERIE_B:
            return []

        sorted_standings = self.get_standings_table()
        # Playoff dal 3° al 6° posto in Serie B
        return [s.team_name for s in sorted_standings[2:6]]

    def simulate_match(self, match) -> Dict:
        """Simula una singola partita usando MatchSimulator unificato"""
        if match.played:
            return {"error": "Partita già giocata"}

        # Ottieni giocatori delle squadre
        home_players = self.get_team_players(match.home_team)
        away_players = self.get_team_players(match.away_team)

        # Usa MatchSimulator unificato che gestisce automaticamente simulazione con/senza giocatori
        result = match_simulator.simulate_match(match, home_players, away_players)

        # Aggiorna classifica solo se non ci sono errori
        if "error" not in result:
            self.update_standings(match)

        return result

    def simulate_matchday(self, matchday: int) -> List[Dict]:
        """Simula tutte le partite di una giornata"""
        matches_to_play = self.get_matches_for_matchday(matchday)

        if not matches_to_play:
            return []

        results = []
        for match in matches_to_play:
            result = self.simulate_match(match)
            if "error" not in result:
                results.append(result)

        if results:
            self.current_matchday = matchday
            logger.info(f"{self.name} - Giornata {matchday}: {len(results)} partite simulate")

        return results

    def is_season_completed(self) -> bool:
        """Verifica se campionato è terminato"""
        return self.current_matchday >= self.max_matchdays

    def get_final_standings(self) -> Dict:
        """Restituisce classifiche finali con zone"""
        if not self.is_season_completed():
            return {"error": "Stagione non ancora completata"}

        standings = self.get_standings_table()

        return {
            "final_table": standings,
            "promoted_teams": self.get_promotion_zone_teams(),
            "relegated_teams": self.get_relegation_zone_teams(),
            "playoff_teams": self.get_playoff_zone_teams(),
            "champion": standings[0].team_name if standings else None
        }

    def get_top_scorers(self) -> List[Dict]:
        """Restituisce classifica marcatori (simulata)"""
        # Implementazione semplificata - in futuro userà stats reali giocatori
        top_scorers = []
        for team in self.teams[:5]:  # Top 5 team
            top_scorers.append({
                "player": f"Attaccante {team}",
                "team": team,
                "goals": random.randint(8, 25)
            })

        return sorted(top_scorers, key=lambda x: x["goals"], reverse=True)

    def get_competition_stats(self) -> Dict:
        """Restituisce statistiche della competizione"""
        total_matches = len([m for m in self.matches if m.played])
        total_goals = sum(m.home_score + m.away_score for m in self.matches if m.played)

        return {
            "name": self.name,
            "level": self.level.value,
            "current_matchday": self.current_matchday,
            "max_matchdays": self.max_matchdays,
            "total_matches_played": total_matches,
            "total_goals": total_goals,
            "average_goals_per_match": total_goals / total_matches if total_matches > 0 else 0,
            "season_completed": self.is_season_completed()
        }

class CompetitionManager:
    """Manager per gestire tutte le competizioni di una stagione"""

    def __init__(self, season: Season):
        self.season = season
        self.competitions: Dict[str, Competition] = {}

    def add_competition(self, competition: Competition):
        """Aggiunge una competizione"""
        self.competitions[competition.name] = competition
        self.season.register_competition(competition.name, competition)
        logger.info(f"Aggiunta competizione: {competition.name}")

    def set_teams_data_all(self, teams_data: Dict):
        """Imposta dati squadre per tutte le competizioni"""
        for competition in self.competitions.values():
            competition.set_teams_data(teams_data)
        logger.info(f"Impostati dati squadre per {len(self.competitions)} competizioni")

    def create_italian_leagues(self, teams_by_level: Dict[int, List[str]], serie_c_groups: Optional[Dict[str, List[str]]] = None):
        """Crea i campionati italiani"""
        level_config = {
            1: (CompetitionLevel.SERIE_A, "Serie A"),
            2: (CompetitionLevel.SERIE_B, "Serie B")
        }

        # Crea Serie A e Serie B normalmente
        for level, teams in teams_by_level.items():
            if teams and level in level_config:
                comp_level, comp_name = level_config[level]

                competition = LeagueCompetition(
                    name=comp_name,
                    season=self.season,
                    teams=teams,
                    level=comp_level
                )

                self.add_competition(competition)

        # Crea Serie C con 3 gironi separati
        if serie_c_groups:
            for group_name, group_teams in serie_c_groups.items():
                if group_teams:
                    competition = LeagueCompetition(
                        name=f"Serie C - {group_name}",
                        season=self.season,
                        teams=group_teams,
                        level=CompetitionLevel.SERIE_C
                    )
                    self.add_competition(competition)

    def get_competition(self, name: str) -> Optional[Competition]:
        """Restituisce competizione per nome"""
        return self.competitions.get(name)

    def get_all_competitions(self) -> List[Competition]:
        """Restituisce tutte le competizioni"""
        return list(self.competitions.values())

    def get_leagues_only(self) -> List[LeagueCompetition]:
        """Restituisce solo i campionati"""
        return [comp for comp in self.competitions.values()
                if isinstance(comp, LeagueCompetition)]

    def get_cups_only(self) -> List:
        """Restituisce solo le coppe"""
        from .cup_competitions import CupCompetition
        return [comp for comp in self.competitions.values()
                if isinstance(comp, CupCompetition)]

    def simulate_all_matchdays(self, matchday: int) -> Dict:
        """Simula giornata per tutte le competizioni"""
        all_results = {}

        for name, competition in self.competitions.items():
            if competition.has_match_on_matchday(matchday):
                results = competition.simulate_matchday(matchday)
                if results:
                    all_results[name] = results

        return all_results

    def get_current_standings(self) -> Dict[str, List[LeagueStanding]]:
        """Restituisce classifiche attuali di tutti i campionati"""
        standings = {}

        for name, competition in self.competitions.items():
            if isinstance(competition, LeagueCompetition):
                standings[name] = competition.get_standings_table()

        return standings

    def get_season_summary(self) -> Dict:
        """Restituisce riassunto stagione competizioni"""
        leagues = self.get_leagues_only()

        summary = {
            "total_competitions": len(self.competitions),
            "leagues": len(leagues),
            "current_matchday": self.season.current_matchday,
            "leagues_completed": len([l for l in leagues if l.is_season_completed()]),
            "competitions_stats": {}
        }

        for competition in leagues:
            summary["competitions_stats"][competition.name] = competition.get_competition_stats()

        return summary

# Funzione utility per setup competizioni italiane
def setup_italian_competitions(season: Season, teams_data: Dict[int, List[str]], serie_c_groups: Optional[Dict[str, List[str]]] = None) -> CompetitionManager:
    """Setup completo competizioni italiane"""
    from .cup_competitions import setup_italian_cups

    manager = CompetitionManager(season)
    manager.create_italian_leagues(teams_data, serie_c_groups)

    # Aggiungi coppe nazionali
    cups = setup_italian_cups(season, teams_data, serie_c_groups or {})
    for cup_name, cup_competition in cups.items():
        manager.add_competition(cup_competition)

    logger.info(f"Setup completato: {len(manager.competitions)} competizioni create (campionati + coppe)")
    return manager
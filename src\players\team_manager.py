"""
Sistema unificato per la gestione e caricamento delle rose squadra
AGGIORNATO: Utilizza il nuovo TeamSquadGenerator basato su reputazione
"""
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass
import time

import sys
import os

# Determina il percorso del progetto
if __name__ == "__main__":
    # Eseguito direttamente
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    src_path = os.path.join(project_root, 'src')
    if src_path not in sys.path:
        sys.path.insert(0, src_path)

try:
    # Prova import relativi prima
    from .player import Player
    from .team_squad_generator import team_squad_generator  # NUOVO SISTEMA
    from .squad_persistence import squad_persistence
    from ..core.utils import logger
    from ..data.data_loader import data_loader
except (ImportErro<PERSON>, ValueError):
    try:
        # Prova import assoluti dal modulo src
        from src.players.player import Player
        from src.players.team_squad_generator import team_squad_generator  # NUOVO SISTEMA
        from src.players.squad_persistence import squad_persistence
        from src.core.utils import logger
        from src.data.data_loader import data_loader
    except ImportError:
        # Ultima risorsa - import diretti
        from players.player import Player
        from players.team_squad_generator import team_squad_generator  # NUOVO SISTEMA
        from players.squad_persistence import squad_persistence
        from core.utils import logger
        from data.data_loader import data_loader


@dataclass
class TeamGenerationConfig:
    """Configurazione per la generazione di una squadra"""
    team_name: str
    team_level: int  # 1=Serie A, 2=Serie B, 3=Serie C, 4=Paesi top, 5=Paesi medi, 6=Paesi bassi
    squad_size: int = 25
    nationality_preference: Optional[str] = None
    # NUOVO: Supporto per dati team reputazione
    team_data: Optional[Dict] = None
    country: Optional[str] = None
    league_name: Optional[str] = None


class TeamManager:
    """Manager unificato per la creazione e gestione delle rose squadra - AGGIORNATO"""

    def __init__(self):
        self.teams_data: Dict[str, List[Player]] = {}
        self.processed_teams: Set[str] = set()
        self.generation_stats = {
            "total_teams": 0,
            "total_players": 0,
            "teams_by_level": {1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0},
            "teams_by_tier": {"Elite": 0, "Top": 0, "Good": 0, "Average": 0, "Weak": 0, "Poor": 0}
        }

        # Carica mappa reputazione nazioni per determinare livelli dinamici
        self.nation_reputation_map = self._load_nation_reputation_data()

    def generate_all_teams_data(self, teams_config: List[TeamGenerationConfig],
                              progress_callback=None) -> Dict[str, List[Player]]:
        """
        Genera i dati di tutte le squadre usando il NUOVO SISTEMA basato su reputazione

        Args:
            teams_config: Lista delle configurazioni squadra
            progress_callback: Funzione callback per aggiornare il progresso (progress, message)

        Returns:
            Dict con nome_squadra -> lista_giocatori
        """
        logger.info(f"Inizio generazione AVANZATA per {len(teams_config)} squadre")

        self.teams_data.clear()
        self.processed_teams.clear()
        self._reset_stats()

        total_teams = len(teams_config)

        for i, config in enumerate(teams_config):
            if config.team_name in self.processed_teams:
                logger.warning(f"Squadra {config.team_name} già processata, saltata")
                continue

            # NUOVO: Usa il sistema basato su reputazione se disponibile
            if config.team_data and config.country and config.league_name:
                squad = self._generate_team_squad_with_reputation(config)
            else:
                # Fallback al sistema precedente per compatibilità
                squad = self._generate_team_squad_legacy(config)

            # Salva dati
            self.teams_data[config.team_name] = squad
            self.processed_teams.add(config.team_name)

            # Aggiorna statistiche avanzate
            self._update_advanced_stats(config, squad)

            # Callback progresso
            if progress_callback:
                progress = int((i + 1) / total_teams * 100)
                progress_callback(progress, f"Generata {config.team_name} (Reputazione: {self._get_team_reputation(config)})")

            logger.debug(f"Squadra generata: {config.team_name} - {len(squad)} giocatori (rep: {self._get_team_reputation(config)})")

        logger.info(f"Generazione completata: {self.generation_stats}")
        return self.teams_data.copy()
    
    def _generate_team_squad_with_reputation(self, config: TeamGenerationConfig) -> List[Player]:
        """Genera squadra usando il NUOVO sistema basato su reputazione"""
        
        # Crea profilo squadra per il nuovo sistema
        profile = team_squad_generator.analyze_team(
            config.team_data, 
            config.country, 
            config.league_name
        )
        
        # Genera la rosa
        squad = team_squad_generator.generate_team_squad(profile, config.squad_size)
        
        # Assegna squadra a tutti i giocatori
        for player in squad:
            player.current_club = config.team_name
        
        return squad

    def _generate_team_squad_legacy(self, config: TeamGenerationConfig) -> List[Player]:
        """RIMOSSO: Era il fallback al vecchio sistema - ora usa sempre il nuovo"""
        # Usa sempre il nuovo sistema
        return self._generate_team_squad_with_reputation(config)

    def _get_team_reputation(self, config: TeamGenerationConfig) -> int:
        """Ottiene la reputazione della squadra"""
        if config.team_data:
            return config.team_data.get("reputazione", 10)
        return 10  # Default

    def _update_advanced_stats(self, config: TeamGenerationConfig, squad: List[Player]):
        """Aggiorna statistiche avanzate includendo tier"""
        self.generation_stats["total_teams"] += 1
        self.generation_stats["total_players"] += len(squad)
        
        if config.team_level in self.generation_stats["teams_by_level"]:
            self.generation_stats["teams_by_level"][config.team_level] += 1
        
        # NUOVO: Statistiche per tier basato su reputazione
        if config.team_data:
            reputation = config.team_data.get("reputazione", 10)
            tier = team_squad_generator._determine_team_tier(reputation)
            self.generation_stats["teams_by_tier"][tier.value] += 1

    def get_team_players(self, team_name: str) -> List[Player]:
        """Restituisce i giocatori di una squadra"""
        return self.teams_data.get(team_name, [])

    def get_all_teams_data(self) -> Dict[str, List[Player]]:
        """Restituisce tutti i dati squadre"""
        return self.teams_data.copy()

    def get_generation_stats(self) -> Dict:
        """Restituisce statistiche della generazione"""
        return self.generation_stats.copy()

    def is_team_loaded(self, team_name: str) -> bool:
        """Verifica se una squadra è già caricata"""
        return team_name in self.processed_teams

    def _reset_stats(self):
        """Reset delle statistiche"""
        self.generation_stats = {
            "total_teams": 0,
            "total_players": 0,
            "teams_by_level": {1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0},
            "teams_by_tier": {"Elite": 0, "Top": 0, "Good": 0, "Average": 0, "Weak": 0, "Poor": 0}
        }

    def _load_nation_reputation_data(self) -> Dict[str, int]:
        """Carica mappa reputazione nazioni da fifa_confederations.json"""
        import json
        from pathlib import Path

        reputation_map = {}

        try:
            fifa_file = Path.cwd() / "fifa_confederations.json"
            if fifa_file.exists():
                with open(fifa_file, 'r', encoding='utf-8') as f:
                    fifa_data = json.load(f)

                # Mappa confederazione -> livello medio
                confederation_levels = {
                    "UEFA": 4,      # Europa: livello alto
                    "CONMEBOL": 4,  # Sud America: livello alto
                    "CONCACAF": 5,  # Nord/Centro America: livello medio
                    "AFC": 5,       # Asia: livello medio
                    "CAF": 5,       # Africa: livello medio
                    "OFC": 6        # Oceania: livello basso
                }

                for country_data in fifa_data:
                    country_name = country_data.get("nome", "")
                    confederation = country_data.get("confederation", "")

                    if country_name and confederation in confederation_levels:
                        reputation_map[country_name] = confederation_levels[confederation]

            logger.info(f"Caricata mappa reputazione per {len(reputation_map)} nazioni")

        except Exception as e:
            logger.warning(f"Errore caricamento reputazione nazioni: {e}")

        return reputation_map

    def _get_team_country(self, team_name: str) -> str:
        """Determina il paese di una squadra dal nome"""
        # Mappa squadre note ai loro paesi
        team_country_map = {
            # Italia
            "Inter Milan": "Italia", "AC Milan": "Italia", "Juventus": "Italia",
            "Napoli": "Italia", "AS Roma": "Italia", "Lazio": "Italia",
            "Atalanta": "Italia", "Fiorentina": "Italia", "Bologna": "Italia",

            # Inghilterra
            "Manchester City": "Inghilterra", "Arsenal": "Inghilterra", "Liverpool": "Inghilterra",
            "Chelsea": "Inghilterra", "Manchester United": "Inghilterra", "Tottenham": "Inghilterra",

            # Spagna
            "Real Madrid": "Spagna", "Barcelona": "Spagna", "Atletico Madrid": "Spagna",
            "Sevilla": "Spagna", "Valencia": "Spagna", "Villarreal": "Spagna",

            # Germania
            "Bayern Munich": "Germania", "Borussia Dortmund": "Germania", "RB Leipzig": "Germania",

            # Francia
            "Paris Saint-Germain": "Francia", "Marseille": "Francia", "Monaco": "Francia",

            # Altri
            "Ajax": "Olanda", "Porto": "Portogallo", "Benfica": "Portogallo"
        }

        return team_country_map.get(team_name, "Sconosciuto")

    def _get_team_level_from_country(self, country: str) -> int:
        """Determina il livello squadra dal paese"""
        return self.nation_reputation_map.get(country, 5)  # Default: livello medio

    # NUOVO: Metodi per persistenza
    def save_all_squads(self, filename: Optional[str] = None) -> bool:
        """Salva tutte le squadre generate"""
        if not self.teams_data:
            logger.warning("Nessuna squadra da salvare")
            return False
        
        # Organizza i dati per paese e campionato per compatibilità
        organized_data = {}
        for team_name, players in self.teams_data.items():
            # Per ora raggruppa tutto sotto "Generated"
            if "Generated" not in organized_data:
                organized_data["Generated"] = {}
            if "Teams" not in organized_data["Generated"]:
                organized_data["Generated"]["Teams"] = {}
            organized_data["Generated"]["Teams"][team_name] = players
        
        return squad_persistence.save_all_squads(organized_data, filename)
    
    def load_saved_squads(self, filename: str) -> bool:
        """Carica squadre salvate"""
        loaded_data = squad_persistence.load_squads(filename)
        if not loaded_data:
            return False
        
        # Riorganizza i dati
        self.teams_data.clear()
        self.processed_teams.clear()
        
        for country, leagues in loaded_data.items():
            for league_name, teams in leagues.items():
                for team_name, players in teams.items():
                    self.teams_data[team_name] = players
                    self.processed_teams.add(team_name)
        
        logger.info(f"Caricate {len(self.teams_data)} squadre da {filename}")
        return True


# Factory functions per creazione configurazioni AGGIORNATE
def create_italian_teams_config() -> List[TeamGenerationConfig]:
    """Crea configurazioni AVANZATE per tutte le squadre italiane"""
    configs = []

    if not data_loader.is_loaded():
        data_loader.load_all_data()

    # Serie A con dati reputazione
    serie_a_teams = data_loader.get_serie_a_teams()
    for team_data in serie_a_teams:
        configs.append(TeamGenerationConfig(
            team_name=team_data["nome"],
            team_level=1,
            nationality_preference="Italia",
            team_data=team_data,  # NUOVO: Dati completi della squadra
            country="Italia",
            league_name="Serie A"
        ))

    # Serie B con dati reputazione
    serie_b_teams = data_loader.get_serie_b_teams()
    for team_data in serie_b_teams:
        configs.append(TeamGenerationConfig(
            team_name=team_data["nome"],
            team_level=2,
            nationality_preference="Italia",
            team_data=team_data,  # NUOVO: Dati completi della squadra
            country="Italia",
            league_name="Serie B"
        ))

    # Serie C con dati reputazione
    serie_c_teams = data_loader.get_serie_c_teams()
    for team_data in serie_c_teams:
        configs.append(TeamGenerationConfig(
            team_name=team_data["nome"],
            team_level=3,
            nationality_preference="Italia",
            team_data=team_data,  # NUOVO: Dati completi della squadra
            country="Italia",
            league_name="Serie C"
        ))

    return configs


def create_uefa_teams_config(uefa_teams: Set[str], processed_teams: Set[str]) -> List[TeamGenerationConfig]:
    """Crea configurazioni AVANZATE per squadre UEFA non ancora processate"""
    configs = []
    manager = TeamManager()

    # NUOVO: Cerca dati reputazione per squadre europee
    if data_loader.is_loaded():
        european_leagues = data_loader.get_all_european_leagues()
        
        for team_name in uefa_teams:
            if team_name not in processed_teams:
                # Cerca la squadra nei dati europei
                team_data = None
                country = None
                league_name = None
                
                for country_name, leagues in european_leagues.items():
                    for league_name_iter, league_data in leagues.items():
                        teams = league_data.get("squadre", [])
                        for team in teams:
                            if team.get("nome") == team_name:
                                team_data = team
                                country = country_name
                                league_name = league_name_iter
                                break
                        if team_data:
                            break
                    if team_data:
                        break
                
                if team_data:
                    # NUOVO: Usa dati reputazione se disponibili
                    configs.append(TeamGenerationConfig(
                        team_name=team_name,
                        team_level=4,  # Default UEFA
                        team_data=team_data,
                        country=country,
                        league_name=league_name
                    ))
                else:
                    # Fallback al sistema vecchio
                    country = manager._get_team_country(team_name)
                    team_level = manager._get_team_level_from_country(country)
                    
                    configs.append(TeamGenerationConfig(
                        team_name=team_name,
                        team_level=team_level
                    ))

    return configs


def create_all_european_teams_config() -> List[TeamGenerationConfig]:
    """NUOVO: Crea configurazioni per TUTTE le squadre europee"""
    configs = []
    
    if not data_loader.is_loaded():
        data_loader.load_all_data()
    
    # Ottieni tutte le squadre europee con dati reputazione
    european_leagues = data_loader.get_all_european_leagues()
    
    for country, leagues in european_leagues.items():
        for league_name, league_data in leagues.items():
            teams = league_data.get("squadre", [])
            for team_data in teams:
                configs.append(TeamGenerationConfig(
                    team_name=team_data["nome"],
                    team_level=4,  # Default per squadre europee
                    team_data=team_data,
                    country=country,
                    league_name=league_name
                ))
    
    logger.info(f"Configurazioni create per {len(configs)} squadre europee")
    return configs


# Istanza globale
team_manager = TeamManager()
"""
Finestra principale dell'applicazione Football Manager Italiano
Frame centrale unico con sistema di navigazione a schermate
"""

import sys
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QApplication, QMenuBar, QStatusBar, QLabel)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QFont

from .screen_manager import ScreenManager
from .screens.new_game_screen import NewGameScreen
from .screens.club_selection_screen import ClubSelectionScreen
from .screens.game_dashboard_screen import GameDashboardScreen
from ..core.config import (WINDOW_TITLE, WINDOW_DEFAULT_WIDTH, WINDOW_DEFAULT_HEIGHT,
                          WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT, MAIN_FONT_FAMILY,
                          MAIN_FONT_SIZE, COLOR_BACKGROUND, COLOR_SURFACE, COLOR_BORDER, COLOR_TEXT)


class MainWindow(QMainWindow):
    """Finestra principale con frame centrale per tutte le schermate"""

    # Segnali per comunicazione tra schermate
    game_started = pyqtSignal(dict)  # Dati della nuova partita
    club_selected = pyqtSignal(dict)  # Dati club e presidente selezionati

    def __init__(self):
        super().__init__()
        self.current_game_data = {}
        self.setup_window()
        self.setup_ui()
        self.setup_connections()
        self.show_new_game_screen()

    def setup_window(self):
        """Configura la finestra principale"""
        self.setWindowTitle(WINDOW_TITLE)
        self.setGeometry(100, 100, WINDOW_DEFAULT_WIDTH, WINDOW_DEFAULT_HEIGHT)
        self.setMinimumSize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT)

        # Font principale
        font = QFont(MAIN_FONT_FAMILY, MAIN_FONT_SIZE)
        self.setFont(font)

        # Stile finestra
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {COLOR_BACKGROUND};
                color: {COLOR_TEXT};
            }}
            QWidget {{
                background-color: {COLOR_BACKGROUND};
                color: {COLOR_TEXT};
                font-family: {MAIN_FONT_FAMILY};
                font-size: {MAIN_FONT_SIZE + 2}px;
            }}
            QLabel {{
                color: {COLOR_TEXT};
                font-family: {MAIN_FONT_FAMILY};
            }}
            QStatusBar {{
                background-color: {COLOR_SURFACE};
                border-top: 1px solid {COLOR_BORDER};
                color: {COLOR_TEXT};
            }}
        """)

    def setup_ui(self):
        """Crea l'interfaccia utente"""
        # Widget centrale
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principale
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Screen Manager - gestisce il frame centrale
        self.screen_manager = ScreenManager()
        main_layout.addWidget(self.screen_manager)

        # Status bar
        self.setup_status_bar()

    def setup_status_bar(self):
        """Configura la barra di stato"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # Info applicazione
        app_info = QLabel("Football Manager Italiano - Pronto")
        status_bar.addWidget(app_info)

        # Info versione
        version_info = QLabel("v1.0")
        status_bar.addPermanentWidget(version_info)

    def setup_connections(self):
        """Collega i segnali tra schermate"""
        self.game_started.connect(self.on_game_started)
        self.club_selected.connect(self.on_club_selected)

    # Navigazione schermate
    def show_new_game_screen(self):
        """Mostra la schermata nuova partita"""
        screen = NewGameScreen()
        screen.game_created.connect(self.game_started.emit)
        self.screen_manager.show_screen(screen)
        self.statusBar().showMessage("Nuova Partita - Configura impostazioni")

    def show_club_selection_screen(self, game_data):
        """Mostra la schermata selezione club"""
        screen = ClubSelectionScreen(game_data)
        screen.club_selected.connect(self.club_selected.emit)
        screen.back_requested.connect(self.show_new_game_screen)
        self.screen_manager.show_screen(screen)
        self.statusBar().showMessage("Selezione Club - Scegli la tua squadra")

    def show_dashboard_screen(self, game_data):
        """Mostra la dashboard principale del gioco"""
        screen = GameDashboardScreen(game_data)
        self.screen_manager.show_screen(screen)

        # Aggiorna status bar con info partita
        club_name = game_data.get('selected_club', {}).get('nome', 'Unknown')
        season = game_data.get('season', '2025/26')
        self.statusBar().showMessage(f"{club_name} - Stagione {season}")

    # Gestori eventi
    def on_game_started(self, game_data):
        """Gestisce la creazione di una nuova partita"""
        self.current_game_data = game_data
        self.show_club_selection_screen(game_data)

    def on_club_selected(self, selection_data):
        """Gestisce la selezione del club"""
        self.current_game_data.update(selection_data)
        self.show_dashboard_screen(self.current_game_data)

    # Metodi utili
    def get_current_screen(self):
        """Restituisce la schermata corrente"""
        return self.screen_manager.current_screen

    def closeEvent(self, event):
        """Gestisce la chiusura dell'applicazione"""
        # Eventuale salvataggio dati
        event.accept()


def main():
    """Funzione principale per avviare l'applicazione"""
    app = QApplication(sys.argv)
    app.setApplicationName(WINDOW_TITLE)

    window = MainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Football Manager Italiano - Interfaccia Grafica
Launcher principale dell'applicazione

Per avviare l'applicazione:
    python football_manager_ui.py
"""

import sys
import os

# Aggiungi il percorso src al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, 'src')
sys.path.insert(0, src_path)

# Importazioni principali
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

# Importa la finestra principale
from ui.main_window import MainWindow


def setup_application():
    """Configura l'applicazione PyQt6"""
    app = QApplication(sys.argv)

    # Configurazioni base
    app.setApplicationName("Football Manager Italiano")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Football Manager Italia")

    # Abilita High DPI support
    app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)

    return app


def check_dependencies():
    """Verifica che tutte le dipendenze siano presenti"""
    required_modules = ['PyQt6', 'json', 'datetime']
    missing_modules = []

    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        error_msg = f"Moduli mancanti: {', '.join(missing_modules)}\n"
        error_msg += "Installa le dipendenze con:\n"
        error_msg += "pip install PyQt6"

        print(f"ERRORE: {error_msg}")
        return False

    return True


def check_data_files():
    """Verifica che i file dati esistano"""
    required_files = [
        'Italia/Serie_A.json',
        'Italia/Serie_B.json',
        'player_names.json',
        'fifa_country_codes.json'
    ]

    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(current_dir, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)

    if missing_files:
        print(f"ATTENZIONE: File dati mancanti: {', '.join(missing_files)}")
        print("L'applicazione potrebbe non funzionare correttamente.")
        return False

    return True


def handle_exception(exc_type, exc_value, exc_traceback):
    """Gestisce le eccezioni non catturate"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    error_msg = f"Errore non gestito:\n{exc_type.__name__}: {exc_value}"
    print(error_msg)

    # Mostra dialog di errore se l'app è attiva
    if QApplication.instance():
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle("Errore Applicazione")
        msg_box.setText("Si è verificato un errore imprevisto.")
        msg_box.setDetailedText(error_msg)
        msg_box.exec()


def main():
    """Funzione principale"""
    print("🏆 Football Manager Italiano - Avvio applicazione...")

    # Verifica dipendenze
    if not check_dependencies():
        input("Premi ENTER per uscire...")
        return 1

    # Verifica file dati
    data_ok = check_data_files()

    # Configura gestione eccezioni
    sys.excepthook = handle_exception

    try:
        # Crea applicazione
        app = setup_application()

        print("✓ Applicazione PyQt6 inizializzata")

        # Crea e mostra finestra principale
        main_window = MainWindow()
        main_window.show()

        print("✓ Finestra principale creata")

        if not data_ok:
            # Mostra avviso per file mancanti
            msg_box = QMessageBox(main_window)
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle("File Dati Mancanti")
            msg_box.setText("Alcuni file dati sono mancanti.")
            msg_box.setInformativeText("L'applicazione funzionerà con dati di esempio.")
            msg_box.exec()

        print("🚀 Applicazione avviata con successo!")
        print("   - Interfaccia grafica attiva")
        print("   - Sistema pannelli caricato")
        print("   - Pronto per l'uso")

        # Avvia loop applicazione
        return app.exec()

    except Exception as e:
        print(f"❌ Errore durante l'avvio: {e}")
        input("Premi ENTER per uscire...")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
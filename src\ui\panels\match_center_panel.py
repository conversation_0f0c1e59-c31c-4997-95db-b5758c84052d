"""
Pannello centro partite con calendario e gestione match
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QGroupBox, QTableWidget, QTableWidgetItem,
                            QHeaderView, QTabWidget, QTextEdit, QProgressBar)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QDateTime
from PyQt6.QtGui import QFont

from ..components.action_buttons import MatchActions, SimulationControls
from ..components.info_display import InfoDisplay
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           COLOR_SUCCESS, COLOR_WARNING, MAIN_FONT_FAMILY,
                           HEADER_FONT_SIZE)


class MatchCenterPanel(QWidget):
    """Pannello centrale per gestione partite e simulazione"""

    match_selected = pyqtSignal(dict)
    simulation_requested = pyqtSignal(str, dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.matches_data = []
        self.selected_match = None
        self.is_simulating = False
        self.setup_ui()
        self.setup_timer()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header con controlli simulazione
        self.create_simulation_header(main_layout)

        # Layout principale con tab
        self.create_main_tabs(main_layout)

    def create_simulation_header(self, parent_layout):
        """Crea l'header con controlli di simulazione"""
        header_widget = QWidget()
        header_widget.setFixedHeight(80)
        header_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {COLOR_SURFACE};
                border: 2px solid {COLOR_PRIMARY};
                border-radius: 10px;
            }}
        """)

        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 15, 20, 15)

        # Info temporale
        time_layout = QVBoxLayout()

        self.current_date_label = QLabel("1 Luglio 2025")
        self.current_date_label.setFont(QFont(MAIN_FONT_FAMILY, 14, QFont.Weight.Bold))
        self.current_date_label.setStyleSheet(f"color: {COLOR_PRIMARY};")

        self.time_status_label = QLabel("Stagione 2025/26 - Preparazione")
        self.time_status_label.setStyleSheet("color: #666;")

        time_layout.addWidget(self.current_date_label)
        time_layout.addWidget(self.time_status_label)

        header_layout.addLayout(time_layout)
        header_layout.addStretch()

        # Controlli simulazione
        self.simulation_controls = SimulationControls()
        self.simulation_controls.action_triggered.connect(self.on_simulation_action)
        header_layout.addWidget(self.simulation_controls)

        parent_layout.addWidget(header_widget)

    def create_main_tabs(self, parent_layout):
        """Crea le tab principali del centro partite"""
        self.tab_widget = QTabWidget()

        # Tab Calendario
        self.create_calendar_tab()

        # Tab Risultati
        self.create_results_tab()

        # Tab Simulazione Live
        self.create_live_simulation_tab()

        parent_layout.addWidget(self.tab_widget)

    def create_calendar_tab(self):
        """Crea la tab calendario partite"""
        calendar_widget = QWidget()
        layout = QVBoxLayout(calendar_widget)

        # Filtri calendario
        filters_layout = QHBoxLayout()
        filters_layout.addWidget(QLabel("Prossime partite:"))
        filters_layout.addStretch()
        layout.addLayout(filters_layout)

        # Tabella partite
        self.matches_table = QTableWidget()
        self.matches_table.setColumnCount(6)
        self.matches_table.setHorizontalHeaderLabels([
            "Data", "Competizione", "Casa", "Trasferta", "Orario", "Stato"
        ])

        # Configura tabella
        header = self.matches_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)

        self.matches_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.matches_table.setAlternatingRowColors(True)
        self.matches_table.itemSelectionChanged.connect(self.on_match_selected)

        layout.addWidget(self.matches_table)

        # Azioni partita
        self.match_actions = MatchActions()
        self.match_actions.action_triggered.connect(self.on_match_action)
        layout.addWidget(self.match_actions)

        self.tab_widget.addTab(calendar_widget, "📅 Calendario")

    def create_results_tab(self):
        """Crea la tab risultati"""
        results_widget = QWidget()
        layout = QVBoxLayout(results_widget)

        # Info risultati recenti
        recent_label = QLabel("Risultati Recenti")
        recent_label.setFont(QFont(MAIN_FONT_FAMILY, 12, QFont.Weight.Bold))
        layout.addWidget(recent_label)

        # Tabella risultati
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            "Data", "Casa", "Risultato", "Trasferta", "Competizione"
        ])

        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)

        self.results_table.setAlternatingRowColors(True)
        layout.addWidget(self.results_table)

        self.tab_widget.addTab(results_widget, "📊 Risultati")

    def create_live_simulation_tab(self):
        """Crea la tab simulazione live"""
        live_widget = QWidget()
        layout = QVBoxLayout(live_widget)

        # Stato simulazione
        self.simulation_status = QLabel("Simulazione ferma")
        self.simulation_status.setFont(QFont(MAIN_FONT_FAMILY, 12, QFont.Weight.Bold))
        self.simulation_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.simulation_status)

        # Barra progresso
        self.simulation_progress = QProgressBar()
        self.simulation_progress.setVisible(False)
        layout.addWidget(self.simulation_progress)

        # Log eventi
        self.events_log = QTextEdit()
        self.events_log.setReadOnly(True)
        self.events_log.setMaximumHeight(200)
        self.events_log.setPlaceholderText("Gli eventi della simulazione appariranno qui...")
        layout.addWidget(self.events_log)

        # Info partita corrente
        self.current_match_info = MatchInfoDisplay()
        layout.addWidget(self.current_match_info)

        layout.addStretch()

        self.tab_widget.addTab(live_widget, "⚡ Live")

    def setup_timer(self):
        """Configura il timer per aggiornamenti"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_simulation)
        # Timer non avviato per default

    def load_matches_data(self):
        """Carica i dati delle partite"""
        # Genera partite di esempio
        self.matches_data = self.generate_sample_matches()
        self.populate_matches_table()

    def generate_sample_matches(self):
        """Genera partite di esempio"""
        sample_matches = [
            {
                "data": "2025-07-15", "competizione": "Amichevole",
                "casa": "Juventus", "trasferta": "Real Madrid",
                "orario": "20:45", "stato": "Programmata"
            },
            {
                "data": "2025-07-20", "competizione": "Amichevole",
                "casa": "Barcelona", "trasferta": "Juventus",
                "orario": "18:30", "stato": "Programmata"
            },
            {
                "data": "2025-08-20", "competizione": "Serie A",
                "casa": "Juventus", "trasferta": "Como",
                "orario": "20:45", "stato": "Programmata"
            }
        ]
        return sample_matches

    def populate_matches_table(self):
        """Popola la tabella partite"""
        self.matches_table.setRowCount(len(self.matches_data))

        for row, match in enumerate(self.matches_data):
            self.matches_table.setItem(row, 0, QTableWidgetItem(match["data"]))
            self.matches_table.setItem(row, 1, QTableWidgetItem(match["competizione"]))
            self.matches_table.setItem(row, 2, QTableWidgetItem(match["casa"]))
            self.matches_table.setItem(row, 3, QTableWidgetItem(match["trasferta"]))
            self.matches_table.setItem(row, 4, QTableWidgetItem(match["orario"]))

            stato_item = QTableWidgetItem(match["stato"])
            if match["stato"] == "Programmata":
                stato_item.setForeground(Qt.GlobalColor.blue)
            self.matches_table.setItem(row, 5, stato_item)

    def on_match_selected(self):
        """Gestisce la selezione di una partita"""
        current_row = self.matches_table.currentRow()
        if 0 <= current_row < len(self.matches_data):
            self.selected_match = self.matches_data[current_row]
            self.match_selected.emit(self.selected_match)

    def on_match_action(self, action, data):
        """Gestisce le azioni sulle partite"""
        if self.selected_match:
            action_data = {
                'action': action,
                'match': self.selected_match,
                'extra': data
            }
            self.simulation_requested.emit(action, action_data)

    def on_simulation_action(self, action, data):
        """Gestisce i controlli di simulazione"""
        if action == "play":
            self.start_simulation()
        elif action == "pause":
            self.pause_simulation()
        elif action == "stop":
            self.stop_simulation()
        elif action == "next":
            self.advance_simulation()

    def start_simulation(self):
        """Avvia la simulazione"""
        self.is_simulating = True
        self.simulation_controls.set_playing_state(True)
        self.simulation_status.setText("Simulazione in corso...")
        self.simulation_progress.setVisible(True)
        self.update_timer.start(1000)  # Aggiorna ogni secondo

        self.add_event_log("🟢 Simulazione avviata")

    def pause_simulation(self):
        """Mette in pausa la simulazione"""
        self.is_simulating = False
        self.simulation_controls.set_playing_state(False)
        self.simulation_status.setText("Simulazione in pausa")
        self.update_timer.stop()

        self.add_event_log("⏸ Simulazione in pausa")

    def stop_simulation(self):
        """Ferma la simulazione"""
        self.is_simulating = False
        self.simulation_controls.set_playing_state(False)
        self.simulation_status.setText("Simulazione ferma")
        self.simulation_progress.setVisible(False)
        self.update_timer.stop()

        self.add_event_log("🔴 Simulazione fermata")

    def advance_simulation(self):
        """Avanza la simulazione al prossimo evento"""
        self.add_event_log("⏭ Avanzamento al prossimo evento")

    def update_simulation(self):
        """Aggiorna la simulazione"""
        if self.is_simulating:
            # Simula progresso
            current_value = self.simulation_progress.value()
            self.simulation_progress.setValue((current_value + 1) % 100)

    def add_event_log(self, message):
        """Aggiunge un messaggio al log eventi"""
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")
        self.events_log.append(f"[{timestamp}] {message}")

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data
        self.load_matches_data()

    def refresh_display(self):
        """Aggiorna la visualizzazione"""
        self.load_matches_data()

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'match_center',
            'selected_match': self.selected_match,
            'matches_count': len(self.matches_data),
            'is_simulating': self.is_simulating
        }


class MatchInfoDisplay(InfoDisplay):
    """Display per informazioni partita corrente"""

    def __init__(self, parent=None):
        super().__init__("Partita Corrente", parent)

    def refresh_display(self):
        """Aggiorna il display con info partita"""
        if not self.info_data:
            self.clear_content()
            no_match = QLabel("Nessuna partita in corso")
            no_match.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_match.setStyleSheet("color: #999; font-style: italic; padding: 20px;")
            self.add_custom_widget(no_match)
            return

        self.clear_content()

        # Info partita
        vs_text = f"{self.info_data.get('casa', '')} vs {self.info_data.get('trasferta', '')}"
        self.add_highlight_value("", vs_text, COLOR_PRIMARY)

        self.add_info_row("Data", self.info_data.get('data', 'N/A'))
        self.add_info_row("Competizione", self.info_data.get('competizione', 'N/A'))
        self.add_info_row("Orario", self.info_data.get('orario', 'N/A'))
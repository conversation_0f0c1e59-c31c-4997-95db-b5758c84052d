"""
Pannello gestione tattiche e formazioni
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QGroupBox, QComboBox, QGridLayout, QPushButton,
                            QTabWidget, QFrame, QScrollArea)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPainter, QPen

from ..components.action_buttons import ActionButtons
from ..components.info_display import InfoDisplay
from ...core.config import (COLOR_SURFACE, COLOR_BORDER, COLOR_PRIMARY,
                           COLOR_SUCCESS, MAIN_FONT_FAMILY, HEADER_FONT_SIZE)


class TacticsPanel(QWidget):
    """Pannello per gestione tattiche e formazioni"""

    formation_changed = pyqtSignal(dict)
    tactic_saved = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_data = {}
        self.current_formation = "4-3-3"
        self.current_tactic = {}
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia del pannello"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header del pannello
        self.create_panel_header(main_layout)

        # Tab widget principale
        self.tab_widget = QTabWidget()

        # Tab Formazione
        self.create_formation_tab()

        # Tab Istruzioni
        self.create_instructions_tab()

        # Tab Ruoli
        self.create_roles_tab()

        main_layout.addWidget(self.tab_widget)

    def create_panel_header(self, parent_layout):
        """Crea l'header del pannello"""
        header_layout = QHBoxLayout()

        # Titolo
        title_label = QLabel("Gestione Tattiche")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: {MAIN_FONT_FAMILY};
                font-size: {HEADER_FONT_SIZE + 2}px;
                font-weight: bold;
                color: {COLOR_PRIMARY};
                padding: 10px 0px;
            }}
        """)

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Selezione formazione rapida
        header_layout.addWidget(QLabel("Formazione:"))
        self.formation_combo = QComboBox()
        self.formation_combo.addItems([
            "4-3-3", "4-4-2", "3-5-2", "4-2-3-1", "5-3-2", "3-4-3"
        ])
        self.formation_combo.setCurrentText(self.current_formation)
        self.formation_combo.currentTextChanged.connect(self.on_formation_changed)
        header_layout.addWidget(self.formation_combo)

        parent_layout.addLayout(header_layout)

    def create_formation_tab(self):
        """Crea la tab formazione"""
        formation_widget = QWidget()
        layout = QVBoxLayout(formation_widget)

        # Campo di gioco
        self.field_widget = FootballField()
        self.field_widget.setMinimumSize(400, 600)
        layout.addWidget(self.field_widget)

        # Controlli formazione
        controls_layout = QHBoxLayout()
        
        save_formation_btn = QPushButton("Salva Formazione")
        save_formation_btn.clicked.connect(self.save_formation)
        controls_layout.addWidget(save_formation_btn)

        reset_formation_btn = QPushButton("Reset")
        reset_formation_btn.clicked.connect(self.reset_formation)
        controls_layout.addWidget(reset_formation_btn)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        self.tab_widget.addTab(formation_widget, "⚽ Formazione")

    def create_instructions_tab(self):
        """Crea la tab istruzioni tattiche"""
        instructions_widget = QWidget()
        layout = QVBoxLayout(instructions_widget)

        # Scroll area per le istruzioni
        scroll = QScrollArea()
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # Istruzioni offensive
        offensive_group = QGroupBox("Istruzioni Offensive")
        offensive_layout = QGridLayout(offensive_group)

        instructions = [
            ("Stile di gioco", ["Possesso", "Contropiede", "Diretto", "Tiki-taka"]),
            ("Pressing", ["Alto", "Medio", "Basso"]),
            ("Larghezza", ["Stretta", "Normale", "Larga"]),
            ("Tempo", ["Lento", "Normale", "Veloce"])
        ]

        self.instruction_combos = {}
        for i, (label, options) in enumerate(instructions):
            offensive_layout.addWidget(QLabel(f"{label}:"), i, 0)
            combo = QComboBox()
            combo.addItems(options)
            combo.setCurrentIndex(1)  # Default "Normale"
            self.instruction_combos[label] = combo
            offensive_layout.addWidget(combo, i, 1)

        scroll_layout.addWidget(offensive_group)

        # Istruzioni difensive
        defensive_group = QGroupBox("Istruzioni Difensive")
        defensive_layout = QGridLayout(defensive_group)

        defensive_instructions = [
            ("Linea difensiva", ["Alta", "Normale", "Bassa"]),
            ("Marcatura", ["A uomo", "A zona", "Mista"]),
            ("Fuorigioco", ["Attivo", "Normale", "Passivo"]),
            ("Aggressività", ["Bassa", "Normale", "Alta"])
        ]

        for i, (label, options) in enumerate(defensive_instructions):
            defensive_layout.addWidget(QLabel(f"{label}:"), i, 0)
            combo = QComboBox()
            combo.addItems(options)
            combo.setCurrentIndex(1)  # Default "Normale"
            self.instruction_combos[label] = combo
            defensive_layout.addWidget(combo, i, 1)

        scroll_layout.addWidget(defensive_group)

        scroll.setWidget(scroll_content)
        layout.addWidget(scroll)

        self.tab_widget.addTab(instructions_widget, "📋 Istruzioni")

    def create_roles_tab(self):
        """Crea la tab ruoli individuali"""
        roles_widget = QWidget()
        layout = QVBoxLayout(roles_widget)

        info_label = QLabel("Ruoli e istruzioni individuali per ogni giocatore")
        info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(info_label)

        # Placeholder per ruoli individuali
        roles_info = InfoDisplay("Ruoli Giocatori")
        layout.addWidget(roles_info)

        layout.addStretch()

        self.tab_widget.addTab(roles_widget, "👤 Ruoli")

    def on_formation_changed(self, formation):
        """Gestisce il cambio di formazione"""
        self.current_formation = formation
        self.field_widget.set_formation(formation)
        self.formation_changed.emit({'formation': formation})

    def save_formation(self):
        """Salva la formazione corrente"""
        tactic_data = {
            'formation': self.current_formation,
            'instructions': {key: combo.currentText() 
                           for key, combo in self.instruction_combos.items()},
            'positions': self.field_widget.get_positions()
        }
        self.current_tactic = tactic_data
        self.tactic_saved.emit(tactic_data)

    def reset_formation(self):
        """Reset della formazione"""
        self.field_widget.reset_positions()

    def set_game_data(self, game_data):
        """Imposta i dati di gioco"""
        self.game_data = game_data

    def get_panel_data(self):
        """Restituisce i dati del pannello"""
        return {
            'type': 'tactics',
            'formation': self.current_formation,
            'tactic': self.current_tactic
        }


class FootballField(QWidget):
    """Widget campo di calcio per posizionamento giocatori"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.formation = "4-3-3"
        self.player_positions = {}
        self.setup_default_positions()

    def setup_default_positions(self):
        """Imposta le posizioni di default"""
        # Posizioni base per 4-3-3
        self.player_positions = {
            'GK': (200, 550),
            'LB': (100, 450), 'CB1': (150, 450), 'CB2': (250, 450), 'RB': (300, 450),
            'CM1': (100, 300), 'CM2': (200, 300), 'CM3': (300, 300),
            'LW': (100, 150), 'ST': (200, 150), 'RW': (300, 150)
        }

    def set_formation(self, formation):
        """Imposta una nuova formazione"""
        self.formation = formation
        self.setup_formation_positions()
        self.update()

    def setup_formation_positions(self):
        """Configura le posizioni per la formazione"""
        formations = {
            "4-3-3": {
                'GK': (200, 550),
                'LB': (100, 450), 'CB1': (150, 450), 'CB2': (250, 450), 'RB': (300, 450),
                'CM1': (100, 300), 'CM2': (200, 300), 'CM3': (300, 300),
                'LW': (100, 150), 'ST': (200, 150), 'RW': (300, 150)
            },
            "4-4-2": {
                'GK': (200, 550),
                'LB': (100, 450), 'CB1': (150, 450), 'CB2': (250, 450), 'RB': (300, 450),
                'LM': (100, 300), 'CM1': (150, 300), 'CM2': (250, 300), 'RM': (300, 300),
                'ST1': (150, 150), 'ST2': (250, 150)
            }
        }
        
        if self.formation in formations:
            self.player_positions = formations[self.formation]

    def paintEvent(self, event):
        """Disegna il campo e i giocatori"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Disegna il campo
        self.draw_field(painter)

        # Disegna i giocatori
        self.draw_players(painter)

    def draw_field(self, painter):
        """Disegna il campo di calcio"""
        # Campo verde
        painter.fillRect(self.rect(), Qt.GlobalColor.green)

        # Linee del campo
        pen = QPen(Qt.GlobalColor.white, 2)
        painter.setPen(pen)

        # Bordi campo
        painter.drawRect(50, 50, 300, 500)

        # Linea di metà campo
        painter.drawLine(50, 300, 350, 300)

        # Cerchio di centrocampo
        painter.drawEllipse(175, 275, 50, 50)

        # Area di rigore
        painter.drawRect(125, 50, 150, 100)
        painter.drawRect(125, 450, 150, 100)

        # Area piccola
        painter.drawRect(150, 50, 100, 50)
        painter.drawRect(150, 500, 100, 50)

    def draw_players(self, painter):
        """Disegna i giocatori"""
        pen = QPen(Qt.GlobalColor.blue, 2)
        painter.setPen(pen)
        painter.setBrush(Qt.GlobalColor.lightBlue)

        for position, (x, y) in self.player_positions.items():
            painter.drawEllipse(x-10, y-10, 20, 20)
            painter.drawText(x-15, y+25, position)

    def get_positions(self):
        """Restituisce le posizioni correnti"""
        return self.player_positions.copy()

    def reset_positions(self):
        """Reset alle posizioni di default"""
        self.setup_formation_positions()
        self.update()

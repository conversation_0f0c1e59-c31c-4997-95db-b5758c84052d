"""
Componente per gestire gruppi di pulsanti azioni
"""

from PyQt6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QPushButton,
                            QButtonGroup, QSizePolicy, QSpacerItem)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from ...core.config import (COLOR_PRIMARY, COLOR_SUCCESS, COLOR_WARNING,
                           COLOR_ERROR, COLOR_SECONDARY, COLOR_TEXT, MAIN_FONT_FAMILY,
                           MAIN_FONT_SIZE)


class ActionButtons(QWidget):
    """Gruppo di pulsanti azioni riutilizzabili"""

    action_triggered = pyqtSignal(str, dict)  # azione, dati extra

    def __init__(self, orientation='horizontal', parent=None):
        super().__init__(parent)
        self.orientation = orientation
        self.buttons = {}
        self.button_group = QButtonGroup()
        self.setup_ui()

    def setup_ui(self):
        """Configura l'interfaccia dei pulsanti"""
        if self.orientation == 'horizontal':
            self.layout = QHBoxLayout(self)
        else:
            self.layout = QVBoxLayout(self)

        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(8)

    def add_button(self, text, action_id, button_type='default', icon=None,
                   tooltip=None, enabled=True):
        """Aggiunge un pulsante al gruppo"""
        button = QPushButton(text)
        button.setMinimumHeight(32)

        # Applica stile basato sul tipo
        self.apply_button_style(button, button_type)

        # Icona
        if icon:
            button.setIcon(icon)

        # Tooltip
        if tooltip:
            button.setToolTip(tooltip)

        # Stato
        button.setEnabled(enabled)

        # Connessione
        button.clicked.connect(lambda: self.action_triggered.emit(action_id, {}))

        # Aggiungi ai containers
        self.buttons[action_id] = button
        self.button_group.addButton(button)
        self.layout.addWidget(button)

        return button

    def apply_button_style(self, button, button_type):
        """Applica lo stile al pulsante basato sul tipo"""
        base_style = f"""
            QPushButton {{
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-family: {MAIN_FONT_FAMILY};
                font-size: {MAIN_FONT_SIZE}px;
                font-weight: bold;
                color: white;
            }}
        """

        if button_type == 'primary':
            color = COLOR_PRIMARY
            hover_color = "#0056b3"
        elif button_type == 'success':
            color = COLOR_SUCCESS
            hover_color = "#218838"
        elif button_type == 'warning':
            color = COLOR_WARNING
            hover_color = "#e0a800"
        elif button_type == 'danger':
            color = COLOR_ERROR
            hover_color = "#c82333"
        elif button_type == 'secondary':
            color = COLOR_SECONDARY
            hover_color = "#5a6268"
        else:  # default
            color = "#6c757d"
            hover_color = "#5a6268"

        style = base_style + f"""
            QPushButton {{
                background-color: {color};
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                transform: translateY(1px);
            }}
            QPushButton:disabled {{
                background-color: #ccc;
                color: #666;
            }}
        """

        button.setStyleSheet(style)

    def add_spacer(self):
        """Aggiunge uno spazio flessibile"""
        spacer = QSpacerItem(20, 20, QSizePolicy.Policy.Expanding,
                            QSizePolicy.Policy.Minimum)
        self.layout.addItem(spacer)

    def remove_button(self, action_id):
        """Rimuove un pulsante"""
        if action_id in self.buttons:
            button = self.buttons[action_id]
            self.button_group.removeButton(button)
            self.layout.removeWidget(button)
            button.deleteLater()
            del self.buttons[action_id]

    def set_button_enabled(self, action_id, enabled):
        """Abilita/disabilita un pulsante specifico"""
        if action_id in self.buttons:
            self.buttons[action_id].setEnabled(enabled)

    def set_button_text(self, action_id, text):
        """Cambia il testo di un pulsante"""
        if action_id in self.buttons:
            self.buttons[action_id].setText(text)

    def get_button(self, action_id):
        """Ottiene un pulsante specifico"""
        return self.buttons.get(action_id)

    def clear_buttons(self):
        """Rimuove tutti i pulsanti"""
        for action_id in list(self.buttons.keys()):
            self.remove_button(action_id)


class QuickActions(ActionButtons):
    """Pulsanti azioni rapide predefiniti"""

    def __init__(self, parent=None):
        super().__init__('horizontal', parent)
        self.setup_quick_actions()

    def setup_quick_actions(self):
        """Configura le azioni rapide comuni"""
        self.add_button("Visualizza", "view", "primary")
        self.add_button("Modifica", "edit", "secondary")
        self.add_button("Elimina", "delete", "danger")


class PlayerActions(ActionButtons):
    """Azioni specifiche per giocatori"""

    def __init__(self, parent=None):
        super().__init__('horizontal', parent)
        self.setup_player_actions()

    def setup_player_actions(self):
        """Configura le azioni per giocatori"""
        self.add_button("Scheda", "view_player", "primary")
        self.add_button("Rinnova", "renew_contract", "success")
        self.add_button("Trasferisci", "transfer", "warning")
        self.add_button("Prestito", "loan", "secondary")


class MatchActions(ActionButtons):
    """Azioni specifiche per partite"""

    def __init__(self, parent=None):
        super().__init__('horizontal', parent)
        self.setup_match_actions()

    def setup_match_actions(self):
        """Configura le azioni per partite"""
        self.add_button("Simula", "simulate", "primary")
        self.add_button("Formazione", "lineup", "secondary")
        self.add_button("Statistiche", "stats", "secondary")


class SimulationControls(ActionButtons):
    """Controlli per la simulazione"""

    def __init__(self, parent=None):
        super().__init__('horizontal', parent)
        self.setup_simulation_controls()

    def setup_simulation_controls(self):
        """Configura i controlli simulazione"""
        self.add_button("▶ Avvia", "play", "success")
        self.add_button("⏸ Pausa", "pause", "warning")
        self.add_button("⏹ Stop", "stop", "danger")
        self.add_button("⏭ Prossima", "next", "primary")

    def set_playing_state(self, is_playing):
        """Aggiorna i pulsanti basato sullo stato di gioco"""
        self.set_button_enabled("play", not is_playing)
        self.set_button_enabled("pause", is_playing)


class FormActions(ActionButtons):
    """Azioni per form (salva, annulla, etc.)"""

    def __init__(self, parent=None):
        super().__init__('horizontal', parent)
        self.setup_form_actions()

    def setup_form_actions(self):
        """Configura le azioni per form"""
        self.add_spacer()
        self.add_button("Annulla", "cancel", "secondary")
        self.add_button("Salva", "save", "success")
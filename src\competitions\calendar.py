"""
Sistema Calendar per gestione calendari partite
"""
import datetime
import random
from typing import List, Dict, Optional, Tuple, TYPE_CHECKING
from enum import Enum
from dataclasses import dataclass

from ..core.utils import logger
from .season import Season
from .calendar_rules import calendar_rules

# Importazioni condizionali per evitare circular imports
if TYPE_CHECKING:
    from .competition import CompetitionManager, LeagueCompetition
    from .cup_competitions import CupCompetition
    from .uefa_competitions import UEFACompetitionManager

class MatchDay(Enum):
    """Giorni della settimana per le partite"""
    MERCOLEDI = 2  # weekday() value
    SABATO = 5
    DOMENICA = 6

class MatchTime(Enum):
    """Orari delle partite"""
    AFTERNOON = "15:00"      # Sabato/Domenica pomeriggio
    EVENING = "20:45"        # Mercoledì sera, alcune domeniche
    LUNCH = "12:30"          # Domenica pranzo
    LATE_AFTERNOON = "18:00" # Sabato sera

@dataclass
class Match:
    """Rappresenta una singola partita"""
    matchday: int
    home_team: str
    away_team: str
    date: datetime.date
    time: str
    competition: str
    venue: str = ""

    # Risultato (None se non giocata)
    home_score: Optional[int] = None
    away_score: Optional[int] = None
    played: bool = False

    # Dettagli aggiuntivi
    attendance: Optional[int] = None
    weather: str = "Buono"

    @property
    def result_string(self) -> str:
        """Restituisce risultato come stringa"""
        if not self.played:
            return "vs"
        return f"{self.home_score}-{self.away_score}"

    @property
    def match_datetime(self) -> datetime.datetime:
        """Combina data e ora in datetime"""
        hour, minute = map(int, self.time.split(':'))
        return datetime.datetime.combine(self.date, datetime.time(hour, minute))

    def __str__(self) -> str:
        return f"G{self.matchday}: {self.home_team} {self.result_string} {self.away_team}"

class CalendarGenerator:
    """Generatore di calendari per campionati"""

    def __init__(self):
        self.match_times = {
            MatchDay.SABATO: [MatchTime.AFTERNOON, MatchTime.LATE_AFTERNOON],
            MatchDay.DOMENICA: [MatchTime.LUNCH, MatchTime.AFTERNOON, MatchTime.EVENING],
            MatchDay.MERCOLEDI: [MatchTime.EVENING]
        }

    def generate_round_robin(self, teams: List[str], double: bool = True) -> List[Tuple[str, str]]:
        """Genera calendario all'italiana (tutti contro tutti)"""
        if len(teams) % 2 != 0:
            teams = teams.copy()
            teams.append("RIPOSO")  # Squadra fittizia per numero dispari

        n = len(teams)
        fixtures = []

        # Algoritmo round-robin standard
        for round_num in range(n - 1):
            round_fixtures = []

            for i in range(n // 2):
                home = teams[i]
                away = teams[n - 1 - i]

                if home != "RIPOSO" and away != "RIPOSO":
                    round_fixtures.append((home, away))

            fixtures.extend(round_fixtures)

            # Ruota le squadre (tranne la prima)
            teams = [teams[0]] + [teams[-1]] + teams[1:-1]

        # Andata e ritorno se richiesto
        if double:
            return_fixtures = [(away, home) for home, away in fixtures]
            fixtures.extend(return_fixtures)

        return fixtures

    def assign_dates_to_fixtures(self, fixtures: List[Tuple[str, str]],
                                season: Season,
                                competition_name: str = "Campionato",
                                calendar: 'MatchCalendar' = None) -> List[Match]:
        """Assegna date e orari alle partite con una logica settimanale."""
        matches = []
        current_date = season.dates.league_start

        # Calcola il numero di squadre e partite per giornata
        all_teams_in_fixtures = set()
        for home, away in fixtures:
            all_teams_in_fixtures.add(home)
            all_teams_in_fixtures.add(away)
        total_teams = len(all_teams_in_fixtures)
        matches_per_matchday = total_teams // 2
        total_matchdays = len(fixtures) // matches_per_matchday

        # Logica per inserire turni infrasettimanali
        midweek_rounds = {5, 10, 15, 20, 25, 30, 35}

        for md in range(1, total_matchdays + 1):
            start_index = (md - 1) * matches_per_matchday
            end_index = md * matches_per_matchday
            matchday_fixtures = fixtures[start_index:end_index]

            # Trova la prossima data valida per il campionato
            # salta se ci sono già partite di coppa
            while not season.is_league_match_day_possible(current_date, calendar):
                current_date += datetime.timedelta(days=1)

            # Determina i giorni per questa giornata (weekend o infrasettimanale)
            available_dates = self._find_available_dates_for_matchday(
                md, current_date, midweek_rounds, calendar, season, competition_name
            )

            # Assegna le partite ai giorni disponibili
            for i, (home_team, away_team) in enumerate(matchday_fixtures):
                chosen_date = self._select_match_date(available_dates, current_date, i, len(matchday_fixtures))

                # Safe enum creation - gestisce giorni non previsti da MatchDay
                weekday = chosen_date.weekday()
                try:
                    match_day_enum = MatchDay(weekday)
                except ValueError:
                    # Se il giorno non è nell'enum, usa default (mercoledì per infrasettimanali, sabato per weekend)
                    match_day_enum = MatchDay.MERCOLEDI if weekday < 5 else MatchDay.SABATO
                    logger.warning(f"Giorno non standard per partita: {chosen_date} ({weekday}), usando default")

                available_times = self.match_times.get(match_day_enum, [MatchTime.AFTERNOON])
                match_time = random.choice(available_times)

                match = Match(
                    matchday=md,
                    home_team=home_team,
                    away_team=away_team,
                    date=chosen_date,
                    time=match_time.value,
                    competition=competition_name,
                    venue=f"Stadio {home_team}"
                )
                matches.append(match)

            # Avanza di una settimana per la prossima giornata
            current_date += datetime.timedelta(days=7)

            if current_date > season.dates.league_end:
                logger.info(f"Calendario {competition_name} completato fino a fine stagione disponibile")
                break

        logger.info(f"Generato calendario per {competition_name} con {len(matches)} partite in {total_matchdays} giornate")
        return matches

    def _find_available_dates_for_matchday(self, matchday: int, current_date: datetime.date,
                                         midweek_rounds: set, calendar: 'MatchCalendar',
                                         season: Season, competition_name: str) -> List[datetime.date]:
        """Trova le date disponibili per una giornata, con logica di fallback robusta."""
        max_attempts = 30  # Limite per evitare loop infiniti
        attempt = 0

        while attempt < max_attempts:
            if matchday in midweek_rounds:
                # Turno infrasettimanale (Mercoledì)
                available_dates = self._find_midweek_dates(current_date, calendar)
            else:
                # Turno nel weekend (Sabato e Domenica)
                available_dates = self._find_weekend_dates(current_date, calendar)

            # Se abbiamo trovato date disponibili, le restituiamo
            if available_dates:
                return available_dates

            # Altrimenti, proviamo la settimana successiva
            current_date += datetime.timedelta(days=7)
            attempt += 1

            # Controlla se siamo oltre la fine della stagione
            if current_date > season.dates.league_end:
                logger.debug(f"Raggiunta fine stagione per {competition_name}, usando date di emergenza")
                return self._get_emergency_dates(current_date)

        # Se arriviamo qui, usiamo date di emergenza
        logger.debug(f"Date libere esaurite per {competition_name} giornata {matchday}, usando date di emergenza")
        return self._get_emergency_dates(current_date)

    def _find_midweek_dates(self, start_date: datetime.date, calendar: 'MatchCalendar') -> List[datetime.date]:
        """Trova date infrasettimanali disponibili (Mercoledì)."""
        available_dates = []

        # Cerca il primo mercoledì a partire dalla data corrente
        match_date = start_date
        while match_date.weekday() != MatchDay.MERCOLEDI.value:
            match_date += datetime.timedelta(days=1)

        # Controlla se il mercoledì è libero
        if not (calendar and calendar.get_matches_for_date(match_date)):
            available_dates.append(match_date)

        return available_dates

    def _find_weekend_dates(self, start_date: datetime.date, calendar: 'MatchCalendar') -> List[datetime.date]:
        """Trova date del weekend disponibili (Sabato e Domenica)."""
        available_dates = []

        # Trova il primo weekend a partire dalla data corrente
        match_date = start_date
        while match_date.weekday() not in [MatchDay.SABATO.value, MatchDay.DOMENICA.value]:
            match_date += datetime.timedelta(days=1)

        # Se siamo su sabato, controlla sabato e domenica
        if match_date.weekday() == MatchDay.SABATO.value:
            sat_date = match_date
            sun_date = sat_date + datetime.timedelta(days=1)

            if not (calendar and calendar.get_matches_for_date(sat_date)):
                available_dates.append(sat_date)
            if not (calendar and calendar.get_matches_for_date(sun_date)):
                available_dates.append(sun_date)

        # Se siamo su domenica, controlla solo domenica
        elif match_date.weekday() == MatchDay.DOMENICA.value:
            if not (calendar and calendar.get_matches_for_date(match_date)):
                available_dates.append(match_date)

        return available_dates

    def _get_emergency_dates(self, base_date: datetime.date) -> List[datetime.date]:
        """Restituisce date di emergenza quando non ci sono alternative."""
        # Cerca il prossimo weekend disponibile entro limiti ragionevoli
        emergency_dates = []

        # Prova le prossime 4 settimane
        for week_offset in range(4):
            test_date = base_date + datetime.timedelta(weeks=week_offset)

            # Trova sabato di quella settimana
            days_to_saturday = (5 - test_date.weekday()) % 7  # 5 = Saturday
            saturday = test_date + datetime.timedelta(days=days_to_saturday)
            emergency_dates.append(saturday)

            # Aggiungi anche domenica
            sunday = saturday + datetime.timedelta(days=1)
            emergency_dates.append(sunday)

        # Se non abbiamo trovato niente, usa la data base
        if not emergency_dates:
            emergency_dates = [base_date]

        return emergency_dates

    def _select_match_date(self, available_dates: List[datetime.date], fallback_date: datetime.date,
                          match_index: int, total_matches: int) -> datetime.date:
        """Seleziona intelligentemente una data per la partita."""
        if not available_dates:
            logger.warning(f"Nessuna data disponibile, usando data di fallback: {fallback_date}")
            return fallback_date

        # Se abbiamo una sola data disponibile, usala
        if len(available_dates) == 1:
            return available_dates[0]

        # Se abbiamo più date, distribuisci le partite
        # Per esempio, se abbiamo sabato e domenica, alterna
        if len(available_dates) == 2:
            return available_dates[match_index % 2]

        # Per più di 2 date, usa distribuzione casuale
        return random.choice(available_dates)

class MatchCalendar:
    """Classe per gestire il calendario delle partite"""

    def __init__(self, season: Season):
        self.season = season
        self.matches: List[Match] = []
        self.matches_by_team: Dict[str, List[Match]] = {}
        self.matches_by_matchday: Dict[int, List[Match]] = {}

    def add_competition_matches(self, matches: List[Match]):
        """Aggiunge partite di una competizione al calendario"""
        self.matches.extend(matches)
        self._rebuild_indexes()

    def _rebuild_indexes(self):
        """Ricostruisce gli indici per ricerca veloce"""
        self.matches_by_team.clear()
        self.matches_by_matchday.clear()

        for match in self.matches:
            # Indice per squadra
            for team in [match.home_team, match.away_team]:
                if team not in self.matches_by_team:
                    self.matches_by_team[team] = []
                self.matches_by_team[team].append(match)

            # Indice per giornata
            if match.matchday not in self.matches_by_matchday:
                self.matches_by_matchday[match.matchday] = []
            self.matches_by_matchday[match.matchday].append(match)

    def get_matches_for_team(self, team_name: str) -> List[Match]:
        """Restituisce tutte le partite di una squadra"""
        return self.matches_by_team.get(team_name, [])

    def get_matches_for_matchday(self, matchday: int) -> List[Match]:
        """Restituisce partite di una giornata"""
        return self.matches_by_matchday.get(matchday, [])

    def get_matches_for_date(self, date: datetime.date) -> List[Match]:
        """Restituisce partite in una data specifica"""
        return [m for m in self.matches if m.date == date]

    def get_next_matches(self, team_name: Optional[str] = None, limit: int = 5) -> List[Match]:
        """Restituisce prossime partite"""
        if team_name:
            team_matches = self.get_matches_for_team(team_name)
            next_matches = [m for m in team_matches if not m.played and m.date >= self.season.current_date]
        else:
            next_matches = [m for m in self.matches if not m.played and m.date >= self.season.current_date]

        # Ordina per data
        next_matches.sort(key=lambda m: (m.date, m.time))
        return next_matches[:limit]

    def get_recent_matches(self, team_name: Optional[str] = None, limit: int = 5) -> List[Match]:
        """Restituisce partite recenti"""
        if team_name:
            team_matches = self.get_matches_for_team(team_name)
            recent_matches = [m for m in team_matches if m.played and m.date <= self.season.current_date]
        else:
            recent_matches = [m for m in self.matches if m.played and m.date <= self.season.current_date]

        # Ordina per data (più recenti prima)
        recent_matches.sort(key=lambda m: (m.date, m.time), reverse=True)
        return recent_matches[:limit]

    def get_matches_in_date_range(self, start_date: datetime.date,
                                 end_date: datetime.date) -> List[Match]:
        """Restituisce partite in un range di date"""
        return [m for m in self.matches if start_date <= m.date <= end_date]

    def has_team_match_on_date(self, team_name: str, date: datetime.date) -> bool:
        """Verifica se una squadra gioca in una data"""
        team_matches = self.get_matches_for_team(team_name)
        return any(m.date == date for m in team_matches)

    def get_free_dates_for_team(self, team_name: str,
                               start_date: datetime.date,
                               end_date: datetime.date) -> List[datetime.date]:
        """Restituisce date libere per una squadra"""
        free_dates = []
        current = start_date

        while current <= end_date:
            if (self.season.is_match_day_possible(current) and
                not self.has_team_match_on_date(team_name, current)):
                free_dates.append(current)
            current += datetime.timedelta(days=1)

        return free_dates

    def schedule_friendly_match(self, home_team: str, away_team: str,
                               date: datetime.date) -> Optional[Match]:
        """Programma un'amichevole"""
        if not self.season.is_match_day_possible(date):
            return None

        if (self.has_team_match_on_date(home_team, date) or
            self.has_team_match_on_date(away_team, date)):
            return None

        # Trova slot temporale libero usando gli orari standard
        match_day = MatchDay(date.weekday())
        # Usa gli stessi orari del generatore di calendario
        match_times = {
            MatchDay.SABATO: [MatchTime.AFTERNOON, MatchTime.LATE_AFTERNOON],
            MatchDay.DOMENICA: [MatchTime.LUNCH, MatchTime.AFTERNOON, MatchTime.EVENING],
            MatchDay.MERCOLEDI: [MatchTime.EVENING]
        }
        available_times = match_times.get(match_day, [MatchTime.AFTERNOON])
        match_time = random.choice(available_times)

        friendly = Match(
            matchday=0,  # Amichevoli hanno matchday 0
            home_team=home_team,
            away_team=away_team,
            date=date,
            time=match_time.value,
            competition="Amichevole",
            venue=f"Stadio {home_team}"
        )

        self.matches.append(friendly)
        self._rebuild_indexes()

        return friendly

    def get_calendar_summary(self) -> Dict:
        """Restituisce riassunto del calendario"""
        total_matches = len(self.matches)
        played_matches = len([m for m in self.matches if m.played])

        competitions = list(set(m.competition for m in self.matches))

        next_matchday_matches = self.get_matches_for_matchday(self.season.current_matchday + 1)

        return {
            "total_matches": total_matches,
            "played_matches": played_matches,
            "remaining_matches": total_matches - played_matches,
            "competitions": competitions,
            "current_matchday": self.season.current_matchday,
            "next_matchday_matches": len(next_matchday_matches) if next_matchday_matches else 0,
            "next_match_date": next_matchday_matches[0].date if next_matchday_matches and len(next_matchday_matches) > 0 else None
        }

    def get_matches_by_month(self, year: int, month: int) -> Dict[int, List[Match]]:
        """Restituisce partite raggruppate per giorno del mese specificato"""
        matches_by_day = {}
        
        for match in self.matches:
            if match.date.year == year and match.date.month == month:
                day = match.date.day
                if day not in matches_by_day:
                    matches_by_day[day] = []
                matches_by_day[day].append(match)
        
        return matches_by_day

    def get_matches_by_competition(self, competition_name: str) -> List[Match]:
        """Restituisce tutte le partite di una specifica competizione"""
        return [m for m in self.matches if m.competition == competition_name]

    def get_match_density_for_date(self, date: datetime.date) -> Dict:
        """Restituisce informazioni sulla densità di partite per una data"""
        matches = self.get_matches_for_date(date)
        
        # Determina il tipo principale di competizione
        competition_types = {"uefa": 0, "cup": 0, "league": 0, "other": 0}
        competitions = set()
        
        for match in matches:
            competitions.add(match.competition)
            comp_lower = match.competition.lower()
            
            if any(uefa in comp_lower for uefa in ["uefa", "champions", "europa", "conference"]):
                competition_types["uefa"] += 1
            elif any(cup in comp_lower for cup in ["coppa", "cup", "trophy"]):
                competition_types["cup"] += 1
            elif any(league in comp_lower for league in ["serie", "premier", "laliga", "bundesliga", "ligue"]):
                competition_types["league"] += 1
            else:
                competition_types["other"] += 1
        
        # Determina il tipo principale
        primary_type = max(competition_types, key=competition_types.get)
        
        return {
            "count": len(matches),
            "matches": matches,
            "primary_type": primary_type,
            "competitions": list(competitions)
        }

    def get_next_important_event(self) -> Optional[Dict]:
        """Restituisce il prossimo evento importante nel calendario"""
        # Ottiene le prossime partite non giocate
        next_matches = [m for m in self.matches if not m.played and m.date >= self.season.current_date]
        
        if not next_matches:
            return None
        
        # Ordina per data
        next_matches.sort(key=lambda m: (m.date, m.time))
        next_match = next_matches[0]
        
        # Calcola giorni rimanenti
        days_to_event = (next_match.date - self.season.current_date).days
        
        event_info = {
            "description": f"{next_match.home_team} vs {next_match.away_team}",
            "date": next_match.date,
            "competition": next_match.competition,
            "days_to_event": max(0, days_to_event),
            "match": next_match
        }
        
        return event_info

    def export_calendar_csv(self, filename: str):
        """Esporta calendario in CSV"""
        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['Giornata', 'Data', 'Ora', 'Casa', 'Trasferta', 'Competizione', 'Risultato']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for match in sorted(self.matches, key=lambda m: (m.date, m.time)):
                writer.writerow({
                    'Giornata': match.matchday,
                    'Data': match.date.strftime('%d/%m/%Y'),
                    'Ora': match.time,
                    'Casa': match.home_team,
                    'Trasferta': match.away_team,
                    'Competizione': match.competition,
                    'Risultato': match.result_string
                })

# Funzioni utility per la generazione calendario

def generate_full_season_calendar(season: Season, competition_manager) -> 'MatchCalendar':
    """Crea calendario completo per una stagione"""
    calendar = MatchCalendar(season)
    generator = CalendarGenerator()

    # 1. Programma le coppe (hanno priorità e date dinamiche)
    # Coppe UEFA (date generate dinamicamente basate su regole)
    uefa_matchdays = calendar_rules.get_uefa_dates(season.start_year)
    logger.info(f"Date UEFA generate: {len(uefa_matchdays)} date disponibili")

    all_competitions = competition_manager.get_all_competitions()
    logger.info(f"Totale competizioni: {len(all_competitions)}")

    for comp in all_competitions:
        logger.info(f"Controllando competizione: {comp.name} (tipo: {type(comp).__name__})")
        # Importazione dinamica per evitare circular import
        from .uefa_competitions import UEFACompetitionManager
        if isinstance(comp, UEFACompetitionManager):
            logger.info(f"✅ Programmazione partite UEFA per {comp.name} con {len(comp.matches)} partite")
            uefa_matches = []
            for match in comp.matches:
                md = match.matchday
                # Converti il numero di matchday in chiave stringa
                md_key = f"MD{md}" if isinstance(md, int) else md
                if md_key in uefa_matchdays:
                    # Assegna data e orario UEFA
                    match.date = uefa_matchdays[md_key]
                    match.time = "21:00" if random.random() > 0.5 else "18:45"
                    uefa_matches.append(match)
                    logger.debug(f"  Programmata partita {match.home_team} vs {match.away_team} per {match.date}")
                else:
                    logger.warning(f"  Nessuna data trovata per {md_key}")

            if uefa_matches:
                calendar.add_competition_matches(uefa_matches)
                logger.info(f"  Aggiunte {len(uefa_matches)} partite UEFA al calendario")
            else:
                logger.warning(f"  Nessuna partita UEFA aggiunta per {comp.name}")
        else:
            logger.debug(f"  Competizione non UEFA: {comp.name}")

    # Coppe Nazionali (date generate dinamicamente)
    coppa_italia_dates = calendar_rules.get_coppa_italia_dates(season.start_year)
    for comp in competition_manager.get_cups_only():
        if comp.name == "Coppa Italia":
            logger.info(f"Generando partite per {comp.name}...")
            cup_matches = _generate_coppa_italia_matches(comp, coppa_italia_dates, season, competition_manager)
            calendar.add_competition_matches(cup_matches)
            logger.info(f"Aggiunte {len(cup_matches)} partite di Coppa Italia al calendario")

        elif comp.name == "Supercoppa Italiana":
            logger.info(f"Generando partite per {comp.name}...")
            supercoppa_matches = _generate_supercoppa_matches(comp, season)
            calendar.add_competition_matches(supercoppa_matches)
            logger.info(f"Aggiunte {len(supercoppa_matches)} partite di Supercoppa al calendario")

        elif comp.name == "Coppa Italia Serie C":
            logger.info(f"Generando partite per {comp.name}...")
            serie_c_cup_matches = _generate_coppa_italia_serie_c_matches(comp, season)
            calendar.add_competition_matches(serie_c_cup_matches)
            logger.info(f"Aggiunte {len(serie_c_cup_matches)} partite di Coppa Italia Serie C al calendario")

    # 2. Programma i campionati negli spazi rimanenti
    leagues = sorted(
        competition_manager.get_leagues_only(),
        key=lambda x: x.level.value
    )

    for league in leagues:
        logger.info(f"Generando calendario per {league.name}...")
        fixtures = generator.generate_round_robin(league.teams, double=True)
        matches = generator.assign_dates_to_fixtures(fixtures, season, league.name, calendar)
        calendar.add_competition_matches(matches)

    return calendar

def generate_italian_league_calendar(*args, **kwargs):
    """
    DEPRECATO: Usare generate_full_season_calendar.
    Mantenuto per compatibilità temporanea.
    """
    logger.warning("generate_italian_league_calendar è deprecato. Usa generate_full_season_calendar.")
    # Implementazione semplificata per non rompere vecchie chiamate
    season = args[0]
    teams_by_level = args[1]
    
    calendar = MatchCalendar(season)
    generator = CalendarGenerator()
    
    # Solo Serie A per il vecchio metodo
    if 1 in teams_by_level:
        fixtures = generator.generate_round_robin(teams_by_level[1], double=True)
        matches = generator.assign_dates_to_fixtures(fixtures, season, "Serie A", calendar)
        calendar.add_competition_matches(matches)
        
    return calendar


def _generate_coppa_italia_matches(coppa_italia, coppa_dates: Dict[str, datetime.date],
                                  season: Season, competition_manager) -> List[Match]:
    """
    Genera SOLO il Turno Preliminare della Coppa Italia.
    Tutti i turni successivi verranno generati dinamicamente dal GameEngine.
    """
    matches = []

    # Setup squadre SOLO per il Turno Preliminare
    _setup_coppa_italia_preliminary_only(coppa_italia, competition_manager)

    # Importa CupRound per evitare circular import
    from .cup_competitions import CupRound

    # Genera SOLO il Turno Preliminare - L'UNICO turno creato inizialmente
    preliminary_teams = coppa_italia.get_teams_for_round(CupRound.PRELIMINARY.value)
    if len(preliminary_teams) >= 2 and CupRound.PRELIMINARY.value in coppa_dates:
        preliminary_date = coppa_dates[CupRound.PRELIMINARY.value]
        preliminary_matches = _create_cup_round_matches(
            coppa_italia, CupRound.PRELIMINARY.value, preliminary_teams, preliminary_date
        )
        matches.extend(preliminary_matches)
        logger.info(f"Coppa Italia - {CupRound.PRELIMINARY.value}: {len(preliminary_matches)} partite programmate per {preliminary_date}")
        logger.info("SISTEMA COMPLETAMENTE DINAMICO: Il Primo Turno verrà generato DOPO i risultati del Turno Preliminare")

    # IMPORTANTE: Nessun altro turno viene generato qui!
    # - Primo Turno: generato dopo Turno Preliminare (4 vincitori + 28 qualificate direttamente)
    # - Secondo Turno: generato dopo Primo Turno (16 vincitori)
    # - Sedicesimi: generato dopo Secondo Turno (8 vincitori + 8 migliori Serie A)
    # - E così via fino alla Finale

    return matches


def _setup_coppa_italia_preliminary_only(coppa_italia, competition_manager):
    """Setup SOLO il Turno Preliminare della Coppa Italia - sistema completamente dinamico"""
    from .cup_competitions import CupRound

    # Ottieni squadre dai campionati
    serie_c_teams = []

    for comp in competition_manager.get_leagues_only():
        if "Serie C" in comp.name:
            serie_c_teams.extend(comp.teams)

    # Setup SOLO il Turno Preliminare - L'UNICO turno configurato inizialmente
    # Turno Preliminare: 8 squadre Serie C più basse (se disponibili)
    if len(serie_c_teams) >= 8:
        preliminary_teams = serie_c_teams[-8:]  # Ultime 8 squadre (più basse)
        coppa_italia.add_teams_for_round(CupRound.PRELIMINARY.value, preliminary_teams)
        logger.info(f"Coppa Italia - Turno Preliminare: {len(preliminary_teams)} squadre qualificate")
        logger.info("SISTEMA SEQUENZIALE: Nessun altro turno configurato - tutto verrà generato dinamicamente")

    # IMPORTANTE: Nessun altro turno viene configurato qui!
    # Tutti i turni successivi verranno creati dinamicamente dal GameEngine:
    #
    # DOPO Turno Preliminare (4 vincitori):
    # - Primo Turno: 4 vincitori Preliminare + 28 qualificate direttamente (16 Serie B + 12 Serie A pos. 9-20)
    #
    # DOPO Primo Turno (16 vincitori):
    # - Secondo Turno: 16 vincitori Primo Turno
    #
    # DOPO Secondo Turno (8 vincitori):
    # - Sedicesimi: 8 vincitori Secondo Turno + 8 migliori Serie A (pos. 1-8)
    #
    # E così via fino alla Finale...


def _create_cup_round_matches(cup_competition, round_name: str, teams: List[str],
                             match_date: datetime.date) -> List[Match]:
    """Crea partite per una fase di coppa"""
    matches = []

    # Genera accoppiamenti
    pairings = _generate_cup_pairings(teams)

    # Determina se è una fase A/R
    from .cup_competitions import CupRound
    is_two_leg = round_name == CupRound.SEMI_FINALS.value

    for i, (home_team, away_team) in enumerate(pairings):
        if is_two_leg:
            # Andata
            andata_match = Match(
                matchday=0,  # Le coppe non hanno matchday
                home_team=home_team,
                away_team=away_team,
                date=match_date,
                time="20:45",
                competition=f"{cup_competition.name} - {round_name} - Andata",
                venue=f"Stadio {home_team}"
            )
            matches.append(andata_match)

            # Ritorno (una settimana dopo)
            ritorno_date = match_date + datetime.timedelta(days=7)
            ritorno_match = Match(
                matchday=0,
                home_team=away_team,
                away_team=home_team,
                date=ritorno_date,
                time="20:45",
                competition=f"{cup_competition.name} - {round_name} - Ritorno",
                venue=f"Stadio {away_team}"
            )
            matches.append(ritorno_match)
        else:
            # Gara secca
            match = Match(
                matchday=0,
                home_team=home_team,
                away_team=away_team,
                date=match_date,
                time="20:45",
                competition=f"{cup_competition.name} - {round_name}",
                venue=f"Stadio {home_team}"
            )
            matches.append(match)

    return matches


def _generate_cup_pairings(teams: List[str]) -> List[tuple]:
    """Genera accoppiamenti casuali per coppe"""
    import random
    teams_copy = teams.copy()
    random.shuffle(teams_copy)

    pairings = []
    for i in range(0, len(teams_copy), 2):
        if i + 1 < len(teams_copy):
            pairings.append((teams_copy[i], teams_copy[i + 1]))

    return pairings


def _generate_supercoppa_matches(supercoppa, season: Season) -> List[Match]:
    """Genera partite per la Supercoppa Italiana"""
    matches = []

    # Data Supercoppa: primo weekend di gennaio
    supercoppa_date = datetime.date(season.start_year + 1, 1, 5)  # Primo sabato di gennaio

    from .cup_competitions import CupRound

    # Semifinali (se ci sono 4 squadre)
    semi1_teams = supercoppa.get_teams_for_round(CupRound.SUPER_SEMI_1.value)
    semi2_teams = supercoppa.get_teams_for_round(CupRound.SUPER_SEMI_2.value)

    if len(semi1_teams) == 2:
        # Semifinale 1
        semi1_match = Match(
            matchday=0,
            home_team=semi1_teams[0],
            away_team=semi1_teams[1],
            date=supercoppa_date,
            time="18:00",
            competition="Supercoppa Italiana - Semifinale 1",
            venue="Stadio Neutro"
        )
        matches.append(semi1_match)

    if len(semi2_teams) == 2:
        # Semifinale 2
        semi2_match = Match(
            matchday=0,
            home_team=semi2_teams[0],
            away_team=semi2_teams[1],
            date=supercoppa_date,
            time="21:00",
            competition="Supercoppa Italiana - Semifinale 2",
            venue="Stadio Neutro"
        )
        matches.append(semi2_match)

    # Finale (3 giorni dopo)
    finale_date = supercoppa_date + datetime.timedelta(days=3)

    # Per ora usiamo le prime due squadre qualificate come finaliste
    # In un sistema completo, queste sarebbero determinate dai risultati delle semifinali
    all_qualified = semi1_teams + semi2_teams
    if len(all_qualified) >= 2:
        finale_match = Match(
            matchday=0,
            home_team=all_qualified[0],
            away_team=all_qualified[1],
            date=finale_date,
            time="20:45",
            competition="Supercoppa Italiana - Finale",
            venue="Stadio Neutro"
        )
        matches.append(finale_match)

    return matches


def _generate_coppa_italia_serie_c_matches(coppa_serie_c, season: Season) -> List[Match]:
    """Genera partite per la Coppa Italia Serie C"""
    matches = []

    from .cup_competitions import CupRound

    # Date per le varie fasi (distribuite durante la stagione)
    dates = {
        CupRound.FIRST_ROUND.value: datetime.date(season.start_year, 9, 15),  # Metà settembre
        CupRound.SECOND_ROUND.value: datetime.date(season.start_year, 10, 20),  # Metà ottobre
        CupRound.QUARTER_FINALS.value: datetime.date(season.start_year, 11, 24),  # Fine novembre
        CupRound.SEMI_FINALS.value: datetime.date(season.start_year + 1, 2, 15),  # Metà febbraio
        CupRound.FINAL.value: datetime.date(season.start_year + 1, 4, 20)  # Metà aprile
    }

    for round_name, round_date in dates.items():
        teams = coppa_serie_c.get_teams_for_round(round_name)

        if len(teams) >= 2:
            round_matches = _create_cup_round_matches(
                coppa_serie_c, round_name, teams, round_date
            )
            matches.extend(round_matches)
            logger.info(f"Coppa Italia Serie C - {round_name}: {len(round_matches)} partite programmate per {round_date}")

    return matches


def generate_next_cup_round(cup_competition, completed_matches: List[Match],
                           season: Season, calendar: 'MatchCalendar') -> List[Match]:
    """
    Genera dinamicamente il prossimo turno di coppa basato sui risultati delle partite giocate.
    Questa funzione viene chiamata dal GameEngine dopo che le partite di un turno sono state simulate.
    """
    from .cup_competitions import CupRound, CupType

    if cup_competition.name != "Coppa Italia":
        return []  # Per ora implementiamo solo la Coppa Italia

    # Determina il turno completato e quello successivo
    completed_round = _determine_completed_round(completed_matches)
    if not completed_round:
        return []

    next_round = _get_next_coppa_italia_round(completed_round)
    if not next_round:
        logger.info(f"Coppa Italia completata - nessun turno successivo dopo {completed_round}")
        return []

    # Ottieni i vincitori del turno completato
    winners = _extract_winners_from_matches(completed_matches)
    if len(winners) < 2:
        logger.warning(f"Vincitori insufficienti per {next_round}: {len(winners)}")
        return []

    # Aggiungi squadre che entrano direttamente in questo turno (se applicabile)
    qualified_teams = _add_direct_qualifiers_for_round(next_round, cup_competition, winners, season)

    # Ottieni la data per il prossimo turno
    from .calendar_rules import calendar_rules
    coppa_dates = calendar_rules.get_coppa_italia_dates(season.start_year)

    if next_round not in coppa_dates:
        logger.warning(f"Data non trovata per {next_round}")
        return []

    next_round_date = coppa_dates[next_round]

    # Genera le partite del prossimo turno
    next_round_matches = _create_cup_round_matches(
        cup_competition, next_round, qualified_teams, next_round_date
    )

    # Aggiorna la coppa con le squadre qualificate
    cup_competition.add_teams_for_round(next_round, qualified_teams)

    logger.info(f"Coppa Italia - {next_round}: {len(next_round_matches)} partite generate dinamicamente per {next_round_date}")
    logger.info(f"Squadre qualificate: {qualified_teams}")

    return next_round_matches


def _determine_completed_round(matches: List[Match]) -> Optional[str]:
    """Determina quale turno è stato completato basandosi sulle partite"""
    if not matches:
        return None

    # Prendi il nome della competizione dalla prima partita
    competition_name = matches[0].competition

    # Estrai il nome del turno
    from .cup_competitions import CupRound
    for round_enum in CupRound:
        if round_enum.value in competition_name:
            return round_enum.value

    return None


def _get_next_coppa_italia_round(current_round: str) -> Optional[str]:
    """Restituisce il prossimo turno della Coppa Italia"""
    from .cup_competitions import CupRound

    round_progression = {
        CupRound.PRELIMINARY.value: CupRound.FIRST_ROUND.value,
        CupRound.FIRST_ROUND.value: CupRound.SECOND_ROUND.value,
        CupRound.SECOND_ROUND.value: CupRound.ROUND_16.value,
        CupRound.ROUND_16.value: CupRound.QUARTER_FINALS.value,
        CupRound.QUARTER_FINALS.value: CupRound.SEMI_FINALS.value,
        CupRound.SEMI_FINALS.value: CupRound.FINAL.value,
        CupRound.FINAL.value: None
    }

    return round_progression.get(current_round)


def _extract_winners_from_matches(matches: List[Match]) -> List[str]:
    """Estrae i vincitori dalle partite giocate"""
    winners = []

    for match in matches:
        if not match.played:
            continue

        # Determina il vincitore
        if match.home_score > match.away_score:
            winners.append(match.home_team)
        elif match.away_score > match.home_score:
            winners.append(match.away_team)
        else:
            # In caso di pareggio nelle coppe, determina vincitore casualmente (simulazione rigori)
            import random
            winner = random.choice([match.home_team, match.away_team])
            winners.append(winner)
            logger.info(f"Pareggio {match.home_team} {match.home_score}-{match.away_score} {match.away_team} - Vincitore ai rigori: {winner}")

    return winners


def _add_direct_qualifiers_for_round(round_name: str, cup_competition, current_winners: List[str], season: Season) -> List[str]:
    """Aggiunge squadre che si qualificano direttamente per un turno specifico secondo il regolamento"""
    from .cup_competitions import CupRound

    qualified_teams = current_winners.copy()

    # PRIMO TURNO: Aggiungere le squadre che entrano direttamente
    if round_name == CupRound.FIRST_ROUND.value:
        # 28 squadre qualificate direttamente: 16 Serie B + 12 Serie A (posizioni 9-20)
        direct_qualifiers = _get_first_round_direct_qualifiers(season)
        qualified_teams.extend(direct_qualifiers)
        logger.info(f"Primo Turno: {len(current_winners)} vincitori Preliminare + {len(direct_qualifiers)} qualificate direttamente")
        logger.info(f"Totale squadre Primo Turno: {len(qualified_teams)}")

    # SEDICESIMI: Aggiungere le 8 migliori squadre di Serie A
    elif round_name == CupRound.ROUND_16.value:
        # 8 migliori squadre di Serie A (posizioni 1-8)
        serie_a_top8 = _get_serie_a_top8(season)
        qualified_teams.extend(serie_a_top8)
        logger.info(f"Sedicesimi: {len(current_winners)} vincitori Secondo Turno + {len(serie_a_top8)} migliori Serie A")
        logger.info(f"Totale squadre Sedicesimi: {len(qualified_teams)}")

    return qualified_teams


def _get_first_round_direct_qualifiers(season: Season) -> List[str]:
    """Ottiene le squadre che si qualificano direttamente al Primo Turno"""
    # FIXME: In un sistema completo, questo dovrebbe ottenere squadre dalla classifiche reali
    # Per ora usiamo squadre mock ma con gestione errori robusta

    # Squadre di esempio - in produzione queste verrebbero dalle classifiche
    mock_serie_b_teams = [
        "Bari", "Brescia", "Catanzaro", "Cittadella", "Cremonese", "Frosinone",
        "Mantova", "Modena", "Palermo", "Pisa", "Reggiana", "Salernitana",
        "Sampdoria", "Spezia", "Cosenza", "Carrarese"
    ]

    mock_serie_a_teams_9_20 = [
        "Udinese", "Empoli", "Parma", "Cagliari", "Hellas Verona", "Monza",
        "Venezia", "Genoa", "Lecce", "Torino", "Bologna", "Como"
    ]

    # Combina le liste ma con safety check
    qualified_teams = []

    # Prendi fino a 16 squadre Serie B
    qualified_teams.extend(mock_serie_b_teams[:16])

    # Prendi fino a 12 squadre Serie A (posizioni 9-20)
    qualified_teams.extend(mock_serie_a_teams_9_20[:12])

    logger.info(f"Coppa Italia - Primo Turno qualificati diretti: {len(qualified_teams)} squadre")
    return qualified_teams


def _get_serie_a_top8(season: Season) -> List[str]:
    """Ottiene le prime 8 squadre di Serie A che entrano ai Sedicesimi"""
    # FIXME: In un sistema completo, questo dovrebbe ottenere le prime 8 dalla classifica Serie A

    mock_top8 = [
        "Juventus", "Inter Milan", "AC Milan", "Napoli",
        "AS Roma", "Lazio", "Atalanta", "Fiorentina"
    ]

    logger.info(f"Coppa Italia - Sedicesimi top8 Serie A: {len(mock_top8)} squadre")
    return mock_top8